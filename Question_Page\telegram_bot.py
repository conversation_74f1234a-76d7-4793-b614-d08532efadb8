"""
Telegram Bot Message Pulling Module

This module handles:
1. Polling Telegram for new messages
2. Saving processed message IDs to avoid duplicates
3. Forwarding messages to webhook for AI processing
4. Sending AI replies back to Telegram users
"""

import asyncio
import json
import logging
import os
import time
import threading
from datetime import datetime
from typing import Dict, Set, Optional, Any

import requests
from telegram import Bot, Update
from telegram.ext import Application, MessageHandler, filters, ContextTypes

# Configure logging first
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO  # Default level, will be updated after config import
)
logger = logging.getLogger(__name__)

# Try to import local config, fall back to default config
try:
    from config_local import *
except ImportError:
    from config import *

# Update logging level after config import
logging.getLogger().setLevel(getattr(logging, LOG_LEVEL, logging.INFO))

# Reduce httpx logging noise (only show warnings and errors)
logging.getLogger("httpx").setLevel(logging.WARNING)

class TelegramMessageProcessor:
    """Handles Telegram message processing and webhook communication."""
    
    def __init__(self):
        # Get configuration variables safely
        self.bot_token = globals().get('TELEGRAM_BOT_TOKEN', 'your-telegram-bot-token-here')
        self.webhook_url = globals().get('TELEGRAM_WEBHOOK_URL', 'http://localhost:5678/webhook/telegram-messages')
        self.polling_interval = globals().get('TELEGRAM_POLLING_INTERVAL', 5)
        self.processed_messages_file = globals().get('TELEGRAM_PROCESSED_MESSAGES_FILE', 'telegram_processed_messages.json')
        self.processed_messages: Set[int] = set()
        self.bot: Optional[Bot] = None
        self.application: Optional[Application] = None
        self.is_running = False
        self.polling_thread: Optional[threading.Thread] = None
        
        # Load processed messages from file
        self._load_processed_messages()
        
        # Initialize bot if token is provided
        if self.bot_token and self.bot_token != 'your-telegram-bot-token-here':
            self._initialize_bot()
        else:
            logger.warning("Telegram bot token not configured. Bot will not start.")
    
    def _initialize_bot(self):
        """Initialize the Telegram bot."""
        try:
            self.bot = Bot(token=self.bot_token)
            # Test the bot connection
            asyncio.run(self._test_bot_connection())
            logger.info("Telegram bot initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Telegram bot: {e}")
            self.bot = None

    async def _test_bot_connection(self):
        """Test bot connection."""
        if self.bot:
            bot_info = await self.bot.get_me()
            logger.info(f"Bot connected: {bot_info.first_name} (@{bot_info.username})")
    
    def _load_processed_messages(self):
        """Load processed message IDs from file."""
        try:
            if os.path.exists(self.processed_messages_file):
                with open(self.processed_messages_file, 'r') as f:
                    data = json.load(f)
                    self.processed_messages = set(data.get('processed_message_ids', []))
                    logger.info(f"Loaded {len(self.processed_messages)} processed message IDs")
            else:
                logger.info("No processed messages file found, starting fresh")
        except Exception as e:
            logger.error(f"Error loading processed messages: {e}")
            self.processed_messages = set()
    
    def _save_processed_messages(self):
        """Save processed message IDs to file."""
        try:
            data = {
                'processed_message_ids': list(self.processed_messages),
                'last_updated': datetime.now().isoformat()
            }
            with open(self.processed_messages_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving processed messages: {e}")
    

    
    async def _forward_to_webhook(self, message, text: str, user_id: int, username: str, is_voice: bool = False):
        """Forward message to the configured webhook."""
        try:
            if not self.webhook_url:
                logger.warning("No webhook URL configured, cannot forward message")
                return

            # Prepare payload for webhook
            payload = {
                "message": text,
                "telegram_message_id": message.message_id,
                "telegram_user_id": user_id,
                "telegram_username": username,
                "telegram_chat_id": message.chat.id,
                "timestamp": message.date.isoformat() if message.date else datetime.now().isoformat(),
                "source": "telegram_bot",
                "message_type": "voice" if is_voice else "text",
                "original_message": {
                    "message_id": message.message_id,
                    "chat_id": message.chat.id,
                    "user_id": user_id,
                    "username": username,
                    "text": text,
                    "is_voice": is_voice,
                    "date": message.date.isoformat() if message.date else None
                }
            }

            message_type = "voice message" if is_voice else "message"
            logger.info(f"Forwarding {message_type} to webhook: {self.webhook_url}")
            logger.debug(f"Webhook payload: {payload}")

            # Send to webhook
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )

            if response.status_code == 200:
                logger.info(f"Successfully forwarded {message_type} {message.message_id} to webhook")
            else:
                logger.error(f"Webhook error {response.status_code}: {response.text}")

        except requests.RequestException as e:
            logger.error(f"Error forwarding to webhook: {e}")
        except Exception as e:
            logger.error(f"Unexpected error in webhook forwarding: {e}")
    
    def send_reply_sync(self, chat_id: int, message: str, reply_to_message_id: Optional[int] = None):
        """Send a reply message to Telegram using direct HTTP request."""
        try:
            if not self.bot_token:
                logger.error("Bot token not configured, cannot send reply")
                return False

            # Use direct HTTP request to Telegram API to avoid event loop issues
            url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"

            payload = {
                "chat_id": chat_id,
                "text": message
            }

            if reply_to_message_id:
                payload["reply_to_message_id"] = reply_to_message_id

            response = requests.post(url, json=payload, timeout=30)

            if response.status_code == 200:
                logger.info(f"Successfully sent reply to chat {chat_id}")
                return True
            else:
                logger.error(f"Telegram API error {response.status_code}: {response.text}")
                return False

        except Exception as e:
            logger.error(f"Error sending reply to Telegram: {e}")
            return False

    async def send_reply(self, chat_id: int, message: str, reply_to_message_id: Optional[int] = None):
        """Send a reply message to Telegram (async wrapper for sync method)."""
        return self.send_reply_sync(chat_id, message, reply_to_message_id)
    
    def start_polling(self):
        """Start the bot polling in a separate thread."""
        if not self.bot:
            logger.error("Bot not initialized, cannot start polling")
            return

        # Stop any existing polling first
        if self.is_running:
            logger.info("Stopping existing bot instance before starting new one")
            self.stop_polling()
            time.sleep(1)  # Give it a moment to stop

        self.is_running = True
        self.polling_thread = threading.Thread(target=self._run_polling, daemon=True)
        self.polling_thread.start()
        logger.info("Started Telegram bot polling in background thread")

    def _run_polling(self):
        """Run the bot polling loop using manual getUpdates."""
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Run manual polling
            loop.run_until_complete(self._manual_polling())
        except Exception as e:
            logger.error(f"Error in polling loop: {e}")
        finally:
            self.is_running = False

    async def _manual_polling(self):
        """Manual polling using getUpdates."""
        offset = 0
        while self.is_running:
            try:
                # Get updates from Telegram (no logging for normal polling)
                updates = await self.bot.get_updates(offset=offset, timeout=self.polling_interval)

                for update in updates:
                    offset = update.update_id + 1

                    # Process text messages
                    if update.message and update.message.text:
                        await self._process_message(update.message)

                    # Process voice messages
                    elif update.message and update.message.voice:
                        await self._process_voice_message(update.message)

            except Exception as e:
                if "Conflict" in str(e):
                    logger.warning(f"Bot conflict detected, stopping this instance")
                    self.is_running = False
                    break
                else:
                    logger.error(f"Error in manual polling: {e}")
                    await asyncio.sleep(self.polling_interval)

    async def _process_message(self, message):
        """Process a single text message."""
        try:
            message_id = message.message_id
            user_id = message.from_user.id
            username = message.from_user.username or message.from_user.first_name
            text = message.text

            # Check if message already processed
            if message_id in self.processed_messages:
                logger.debug(f"Message {message_id} already processed, skipping")
                return

            logger.info(f"Processing new message {message_id} from {username} ({user_id}): {text[:50]}...")

            # Add to processed messages
            self.processed_messages.add(message_id)
            self._save_processed_messages()

            # Forward message to webhook
            await self._forward_to_webhook(message, text, user_id, username)

        except Exception as e:
            logger.error(f"Error processing message: {e}")

    async def _process_voice_message(self, message):
        """Process a voice message by converting it to text first."""
        try:
            message_id = message.message_id
            user_id = message.from_user.id
            username = message.from_user.username or message.from_user.first_name

            # Check if message already processed
            if message_id in self.processed_messages:
                logger.debug(f"Voice message {message_id} already processed, skipping")
                return

            logger.info(f"Processing voice message {message_id} from {username} ({user_id})")

            # Add to processed messages immediately to prevent reprocessing
            self.processed_messages.add(message_id)
            self._save_processed_messages()

            # Download and convert voice to text
            text = await self._convert_voice_to_text(message.voice)

            if text:
                logger.info(f"Voice message converted to text: {text[:50]}...")
                # Forward the converted text to webhook
                await self._forward_to_webhook(message, text, user_id, username, is_voice=True)
            else:
                logger.error(f"Failed to convert voice message {message_id} to text")
                # Send error message back to user
                self.send_reply_sync(
                    chat_id=message.chat.id,
                    message="Sorry, I couldn't process your voice message. Please try again or send a text message.",
                    reply_to_message_id=message_id
                )

        except Exception as e:
            logger.error(f"Error processing voice message: {e}")

    async def _convert_voice_to_text(self, voice):
        """Download voice message and convert to text using Groq."""
        import tempfile
        import os
        from io import BytesIO

        try:
            # Get file info
            file_info = await self.bot.get_file(voice.file_id)

            # Download the voice file
            voice_data = BytesIO()
            await file_info.download_to_memory(voice_data)
            voice_data.seek(0)

            # Convert to text using the same method as the Flask app
            text = self._transcribe_with_groq(voice_data, voice.file_unique_id)

            return text

        except Exception as e:
            logger.error(f"Error converting voice to text: {e}")
            return None

    def _transcribe_with_groq(self, audio_data, filename):
        """Transcribe audio using Groq Whisper API (sync version)."""
        try:
            # Import Groq here to avoid circular imports
            from groq import Groq

            # Get Groq configuration
            groq_api_key = globals().get('GROQ_API_KEY')
            groq_model = globals().get('GROQ_MODEL', 'whisper-large-v3')

            if not groq_api_key:
                logger.error("Groq API key not configured")
                return None

            # Initialize Groq client
            groq_client = Groq(api_key=groq_api_key)

            # Create file tuple for Groq API
            file_tuple = (f"{filename}.ogg", audio_data, "audio/ogg")

            # Call Groq API
            response = groq_client.audio.transcriptions.create(
                model=groq_model,
                file=file_tuple,
                language="en",  # Default to English, can be made configurable
                response_format="text"
            )

            return response

        except Exception as e:
            logger.error(f"Error transcribing with Groq: {e}")
            return None
    
    def stop_polling(self):
        """Stop the bot polling."""
        if self.application and self.is_running:
            self.is_running = False
            # Note: Stopping the application requires more complex handling
            # For now, we'll just mark as not running
            logger.info("Telegram bot polling stopped")

# Global instance - will be initialized when needed
telegram_processor = None

def get_telegram_processor():
    """Get or create the telegram processor instance."""
    global telegram_processor
    if telegram_processor is None:
        telegram_processor = TelegramMessageProcessor()
    return telegram_processor

def start_telegram_bot():
    """Start the Telegram bot."""
    processor = get_telegram_processor()
    processor.start_polling()

def stop_telegram_bot():
    """Stop the Telegram bot."""
    processor = get_telegram_processor()
    processor.stop_polling()

async def send_telegram_reply(chat_id: int, message: str, reply_to_message_id: Optional[int] = None):
    """Send a reply to Telegram."""
    processor = get_telegram_processor()
    return await processor.send_reply(chat_id, message, reply_to_message_id)

if __name__ == "__main__":
    # For testing purposes
    logger.info("Starting Telegram bot...")
    start_telegram_bot()
    
    try:
        # Keep the main thread alive
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Stopping Telegram bot...")
        stop_telegram_bot()
