// Global variables
let availableSources = [];
let detailedSources = [];
let availableModels = [];
let selectedModel = 'RSJARVIS';
let isRecording = false;
let mediaRecorder = null;
let audioChunks = [];
let selectedIndex = -1;
let messageId = 0;
let isPanelCollapsed = false;
let isSettingsPanelCollapsed = true;
let currentFontSize = 'medium';
let userSessionId = null; // Track user session ID

// Realtime upload status (filename/session_id -> simple status string)
const sourceStatus = {};

// Temporary (ephemeral) sources not yet in DB
const ephemeralSources = [];

// DOM elements
const questionInput = document.getElementById('question-input');
const autocompleteDropdown = document.getElementById('autocomplete-dropdown');
const speechButton = document.getElementById('speech-button');
const recordingIndicator = document.getElementById('recording-indicator');
const statusMessage = document.getElementById('status-message');
const loadingOverlay = document.getElementById('loading-overlay');
const loadingText = document.getElementById('loading-text');
const chatContainer = document.getElementById('chat-container');
const welcomeSection = document.getElementById('welcome-section');

// Sources panel elements
const sourcesPanel = document.getElementById('sources-panel');
const panelToggleBtn = document.getElementById('panel-toggle');
const mobileSourcesToggle = document.getElementById('mobile-sources-toggle');
const floatingToggleBtn = document.getElementById('floating-toggle-btn');
const addSourceBtn = document.getElementById('add-source-btn');
const youtubeBtn = document.getElementById('youtube-btn');
// Removed discover sources button
// Removed select-all checkbox functionality
const sourcesList = document.getElementById('sources-list');
const sourcesCount = document.getElementById('sources-count');
const pdfUploadInput = document.getElementById('pdf-upload-input');

// Settings panel elements
const settingsPanel = document.getElementById('settings-panel');
const settingsToggleBtn = document.getElementById('settings-toggle');
const settingsPanelToggle = document.getElementById('settings-panel-toggle');
// Font size buttons will be selected in setupSettingsPanel
const mainContent = document.querySelector('.main-content');

// External link button
const externalLinkBtn = document.getElementById('external-link-btn');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    loadAvailableSources();
    loadAvailableModels();
    loadDetailedSources();
    setupSourcesPanel();
    setupSettingsPanel();
});
    // Logout button
    const logoutBtn = document.getElementById('logout-btn');


async function fetchSessionInfo() {
    try {
        const response = await fetch('/api/session-info');
        if (response.ok) {
            const sessionData = await response.json();
            userSessionId = sessionData.sessionId;
            console.log('Session ID:', userSessionId);
            return sessionData;
        }
    } catch (error) {
        console.error('Failed to fetch session info:', error);
    }
    return null;
}

function initializeApp() {
    console.log('RS Techwin AI Assistant initialized');

    // Fetch session information
    fetchSessionInfo();

    // Initialize Server-Sent Events for real-time responses
    initializeSSE();

    // Lazy load sources after UI is ready to avoid blocking initial paint
    setTimeout(testChromaDBConnection, 100);
}

async function testChromaDBConnection() {
    try {
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 4000);
        const response = await fetch('/api/sources', { signal: controller.signal, cache: 'no-store' });
        clearTimeout(timeout);
        if (response.ok) {
            console.log('ChromaDB connection successful');
        } else {
            console.warn('ChromaDB connection failed:', response.status, response.statusText);
            showStatus('Warning: Document database not available. @ filtering will not work.', 'error');
        }
    } catch (error) {
        console.error('ChromaDB connection error:', error);
        showStatus('Warning: Cannot connect to document database. @ filtering will not work.', 'error');
    }
}

function setupEventListeners() {
    // Text input events
    questionInput.addEventListener('input', handleTextInput);
    questionInput.addEventListener('keydown', handleKeyDown);
    questionInput.addEventListener('blur', hideAutocomplete);

    // Button events
    speechButton.addEventListener('mousedown', startRecording);
    speechButton.addEventListener('mouseup', stopRecording);
    speechButton.addEventListener('mouseleave', stopRecording);
    speechButton.addEventListener('touchstart', startRecording);
    speechButton.addEventListener('touchend', stopRecording);

    // Logout
    if (logoutBtn) {
        logoutBtn.addEventListener('click', async () => {
            try {
                const res = await fetch('/logout');
                if (res.redirected) {
                    window.location.href = res.url;
                } else {
                    window.location.href = '/login';
                }
            } catch (e) {
                window.location.href = '/login';
            }
        });
    }


    // Form submission
    questionInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
}

// Load available models (backend still provides list; UI selection removed)
async function loadAvailableModels() {
    try {
        const response = await fetch('/api/models', { cache: 'no-store' });
        if (response.ok) {
            const data = await response.json();
            availableModels = data.models || [];
            selectedModel = 'RSJARVIS';
            console.log(`Models loaded (selection disabled). Default= ${selectedModel}`);
        } else {
            console.warn('Could not load models; defaulting to RSJARVIS');
        }
    } catch (error) {
        console.warn('Model load error; defaulting to RSJARVIS');
    }
}

// Handle model selection change
function handleModelChange() {
    selectedModel = modelSelect.value;
    console.log(`Model changed to: ${selectedModel}`);

    // Update placeholder text based on model
    if (selectedModel === 'RSJARVIS') {
        questionInput.placeholder = 'Ask RSJARVIS';
    } else {
        questionInput.placeholder = 'Ask ChatGPT';
    }
}

// Autocomplete functionality
async function loadAvailableSources() {
    try {
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 4000);
        const response = await fetch('/api/sources', { signal: controller.signal, cache: 'no-store' });
        clearTimeout(timeout);
        if (response.ok) {
            const data = await response.json();
            availableSources = data.sources || [];
            console.log(`Loaded ${availableSources.length} available sources`);

            if (availableSources.length === 0) {
                console.log('No sources available - autocomplete will not work until documents are uploaded');
            }
        } else {
            console.error('Failed to load sources:', response.statusText);
            showStatus('Warning: Could not load available documents', 'error');
        }
    } catch (error) {
        console.error('Error loading sources:', error);
        showStatus('Warning: Could not connect to document database', 'error');
    }
}

// Function to refresh sources (can be called manually)
async function refreshSources() {
    showStatus('Refreshing document list...', 'info');
    await loadAvailableSources();
    if (availableSources.length > 0) {
        showStatus(`Loaded ${availableSources.length} documents for @ filtering`, 'success');
    } else {
        showStatus('No documents found. Upload documents to enable @ filtering.', 'info');
    }
}

// Add keyboard shortcut to refresh sources (Ctrl+R or Cmd+R when input is focused)
document.addEventListener('keydown', function(e) {
    if ((e.ctrlKey || e.metaKey) && e.key === 'r' && document.activeElement === questionInput) {
        e.preventDefault();
        refreshSources();
    }
});

// Reposition autocomplete dropdown on window resize
window.addEventListener('resize', function() {
    if (!autocompleteDropdown.classList.contains('hidden')) {
        positionAutocompleteDropdown();
    }
});

function handleTextInput(e) {
    const value = e.target.value;
    const cursorPosition = e.target.selectionStart;

    // Check if we should show autocomplete
    const atIndex = value.lastIndexOf('@', cursorPosition - 1);

    if (atIndex !== -1) {
        const afterAt = value.substring(atIndex + 1, cursorPosition);
        const spaceIndex = afterAt.indexOf(' ');

        if (spaceIndex === -1) {
            // We're still typing the filename
            showAutocomplete(afterAt, atIndex);
            return;
        }
    }

    hideAutocomplete();
}

function showAutocomplete(query, atIndex) {
    if (availableSources.length === 0) {
        // Show a message that no documents are available
        autocompleteDropdown.innerHTML = '<div class="autocomplete-item" style="color: #9aa0a6; font-style: italic; cursor: default;">No documents available. Upload documents to use @ filtering.</div>';
        autocompleteDropdown.classList.remove('hidden');
        positionAutocompleteDropdown();
        return;
    }

    const filteredSources = availableSources.filter(source =>
        source.toLowerCase().startsWith(query.toLowerCase())
    );

    autocompleteDropdown.innerHTML = '';
    selectedIndex = -1;

    if (filteredSources.length === 0) {
        // Show no matches message
        autocompleteDropdown.innerHTML = '<div class="autocomplete-item" style="color: #9aa0a6; font-style: italic; cursor: default;">No matching documents found.</div>';
        autocompleteDropdown.classList.remove('hidden');
        positionAutocompleteDropdown();
        return;
    }

    filteredSources.forEach((source) => {
        const item = document.createElement('div');
        item.className = 'autocomplete-item';
        item.textContent = source;
        item.addEventListener('click', () => selectSource(source, atIndex));
        autocompleteDropdown.appendChild(item);
    });

    autocompleteDropdown.classList.remove('hidden');

    // Smart positioning: check if dropdown would go off-screen
    positionAutocompleteDropdown();
}

function positionAutocompleteDropdown() {
    // Get the input element's position and dimensions
    const inputRect = questionInput.getBoundingClientRect();
    const dropdownHeight = 200; // max-height from CSS
    const viewportHeight = window.innerHeight;
    const scrollY = window.scrollY;

    // Calculate space below and above the input
    const spaceBelow = viewportHeight - (inputRect.bottom - scrollY);
    const spaceAbove = inputRect.top - scrollY;

    // Remove any existing positioning classes
    autocompleteDropdown.classList.remove('expand-up');

    // If there's not enough space below but there's more space above, expand upward
    if (spaceBelow < dropdownHeight && spaceAbove > spaceBelow) {
        autocompleteDropdown.classList.add('expand-up');
    }
}

function selectSource(source, atIndex) {
    const currentValue = questionInput.value;
    const beforeAt = currentValue.substring(0, atIndex);
    const afterCursor = currentValue.substring(questionInput.selectionStart);

    questionInput.value = beforeAt + '@' + source + ' ' + afterCursor;
    questionInput.focus();

    // Position cursor after the inserted source
    const newPosition = beforeAt.length + source.length + 2;
    questionInput.setSelectionRange(newPosition, newPosition);

    hideAutocomplete();
}

function hideAutocomplete() {
    setTimeout(() => {
        autocompleteDropdown.classList.add('hidden');
        selectedIndex = -1;
    }, 150);
}

function handleKeyDown(e) {
    if (autocompleteDropdown.classList.contains('hidden')) return;

    const items = autocompleteDropdown.querySelectorAll('.autocomplete-item');

    switch (e.key) {
        case 'ArrowDown':
            e.preventDefault();
            selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
            updateSelection(items);
            break;

        case 'ArrowUp':
            e.preventDefault();
            selectedIndex = Math.max(selectedIndex - 1, -1);
            updateSelection(items);
            break;

        case 'Enter':
            e.preventDefault();
            if (selectedIndex >= 0 && items[selectedIndex]) {
                items[selectedIndex].click();
            }
            break;

        case 'Escape':
            hideAutocomplete();
            break;
    }
}

function updateSelection(items) {
    items.forEach((item, index) => {
        item.classList.toggle('selected', index === selectedIndex);
    });
}

// Speech recognition functionality
async function checkMicrophonePermission() {
    try {
        // Check if the browser supports the Permissions API
        if ('permissions' in navigator) {
            const permission = await navigator.permissions.query({ name: 'microphone' });
            return permission.state;
        }
        return 'prompt'; // Default to prompt if Permissions API not supported
    } catch (error) {
        console.log('Permissions API not supported, will prompt user');
        return 'prompt';
    }
}

async function requestMicrophonePermission() {
    try {
        // Request microphone access - this will show the browser permission dialog
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        // Stop the stream immediately as we just wanted to get permission
        stream.getTracks().forEach(track => track.stop());
        return true;
    } catch (error) {
        console.error('Microphone permission denied:', error);
        return false;
    }
}

async function startRecording() {
    if (isRecording) return;

    // Check microphone permission first
    const permissionState = await checkMicrophonePermission();

    if (permissionState === 'denied') {
        showStatus('Microphone access denied. Please enable microphone permissions in your browser settings and refresh the page.', 'error');
        return;
    }

    if (permissionState === 'prompt') {
        showStatus('Requesting microphone permission...', 'info');
        const hasPermission = await requestMicrophonePermission();
        if (!hasPermission) {
            showStatus('Microphone permission denied. Please allow microphone access to use voice input.', 'error');
            return;
        }
    }

    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

        mediaRecorder = new MediaRecorder(stream);
        audioChunks = [];

        mediaRecorder.ondataavailable = (event) => {
            audioChunks.push(event.data);
        };

        mediaRecorder.onstop = async () => {
            const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
            await transcribeAudio(audioBlob);

            // Stop all tracks to release microphone
            stream.getTracks().forEach(track => track.stop());
        };

        mediaRecorder.start();
        isRecording = true;

        speechButton.classList.add('recording');
        recordingIndicator.classList.remove('hidden');

        // Clear any previous status messages
        hideStatus();

    } catch (error) {
        console.error('Error accessing microphone:', error);
        if (error.name === 'NotAllowedError') {
            showStatus('Microphone permission denied. Please allow microphone access and try again.', 'error');
        } else if (error.name === 'NotFoundError') {
            showStatus('No microphone found. Please connect a microphone and try again.', 'error');
        } else {
            showStatus('Error accessing microphone. Please check your microphone settings.', 'error');
        }
    }
}

function stopRecording() {
    if (!isRecording || !mediaRecorder) return;

    mediaRecorder.stop();
    isRecording = false;

    speechButton.classList.remove('recording');
    recordingIndicator.classList.add('hidden');
}

async function transcribeAudio(audioBlob) {
    try {
        showLoading('Transcribing audio...');

        const formData = new FormData();
        formData.append('audio', audioBlob, 'recording.wav');

        const response = await fetch('/api/speech-to-text', {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const data = await response.json();
            const transcribedText = data.text;

            // Insert transcribed text into input field
            const currentValue = questionInput.value;
            const cursorPosition = questionInput.selectionStart;
            const newValue = currentValue.slice(0, cursorPosition) +
                           (currentValue ? ' ' : '') + transcribedText +
                           currentValue.slice(cursorPosition);

            questionInput.value = newValue;
            questionInput.focus();

            showStatus('Speech transcribed successfully!', 'success');
        } else {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Transcription failed');
        }
    } catch (error) {
        console.error('Transcription error:', error);
        showStatus(`Transcription failed: ${error.message}`, 'error');
    } finally {
        hideLoading();
    }
}

// Message sending functionality
async function sendMessage() {
    const message = questionInput.value.trim();

    if (!message) {
        showStatus('Please enter a message before sending.', 'error');
        return;
    }

    // Add user message to chat
    addMessageToChat(message, 'user');

    // Clear input and show typing indicator
    questionInput.value = '';
    showTypingIndicator();

    try {
        const currentMessageId = ++messageId;

        // All sources are now used by default (no selection needed)

        const response = await fetch('/api/send-message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Timestamp': new Date().toISOString()
            },
            body: JSON.stringify({
                message: message,
                messageId: currentMessageId,
                model: selectedModel
            })
        });

        if (response.ok) {
            const responseData = await response.json();
            // Update session ID if provided
            if (responseData.sessionId && responseData.sessionId !== userSessionId) {
                userSessionId = responseData.sessionId;
                console.log('Updated session ID:', userSessionId);
            }
            showStatus('Message sent! Waiting for AI response...', 'info');
        } else {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to send message');
        }
    } catch (error) {
        console.error('Send message error:', error);
        hideTypingIndicator();
        addMessageToChat(`Sorry, I encountered an error: ${error.message}`, 'ai');
        showStatus(`Failed to send message: ${error.message}`, 'error');
    }
}

// Chat functionality
function addMessageToChat(message, sender) {
    // Hide welcome section when first message is added
    if (welcomeSection && !welcomeSection.classList.contains('hidden')) {
        welcomeSection.classList.add('hidden');
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;

    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';

    if (sender === 'user') {
        contentDiv.innerHTML = escapeHtml(message);
    } else {
        contentDiv.innerHTML = escapeHtml(message);
    }

    const timeDiv = document.createElement('div');
    timeDiv.className = 'message-time';
    timeDiv.textContent = new Date().toLocaleTimeString();

    messageDiv.appendChild(contentDiv);
    messageDiv.appendChild(timeDiv);

    chatContainer.appendChild(messageDiv);
    scrollToBottom();
}

function showTypingIndicator() {
    const typingDiv = document.createElement('div');
    typingDiv.className = 'typing-indicator';
    typingDiv.id = 'typing-indicator';

    const dotsDiv = document.createElement('div');
    dotsDiv.className = 'typing-dots';

    for (let i = 0; i < 3; i++) {
        const dot = document.createElement('div');
        dot.className = 'typing-dot';
        dotsDiv.appendChild(dot);
    }

    typingDiv.appendChild(dotsDiv);
    chatContainer.appendChild(typingDiv);
    scrollToBottom();
}

function hideTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

function scrollToBottom() {
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    let escaped = div.innerHTML;

    // Handle code blocks FIRST (before converting newlines)
    // Match ```language\ncode\n``` format
    escaped = escaped.replace(/```(\w+)?\n([\s\S]*?)\n```/g, function(_, language, code) {
        const lang = language || '';
        return `<pre><code class="language-${lang}">${code}</code></pre>`;
    });

    // Handle code blocks without language
    escaped = escaped.replace(/```\n([\s\S]*?)\n```/g, '<pre><code>$1</code></pre>');

    // Handle code blocks on same line
    escaped = escaped.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

    // Convert remaining newlines to HTML line breaks (but not inside code blocks)
    escaped = escaped.replace(/\n/g, '<br>');

    // Handle basic markdown formatting (after HTML escaping)
    escaped = escaped
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')  // **bold**
        .replace(/\*(.*?)\*/g, '<em>$1</em>')              // *italic*
        .replace(/`(.*?)`/g, '<code>$1</code>');           // `code`

    return escaped;
}

// Server-Sent Events for real-time responses
function initializeSSE() {
    console.log('Initializing Server-Sent Events...');

    const eventSource = new EventSource('/api/stream-responses');

    eventSource.onmessage = function(event) {
        try {
            const responseData = JSON.parse(event.data);
            console.log('Received SSE data:', responseData);

            // Handle heartbeat messages
            if (responseData.type === 'heartbeat') {
                // Update session ID if provided in heartbeat
                if (responseData.sessionId && responseData.sessionId !== userSessionId) {
                    userSessionId = responseData.sessionId;
                    console.log('Session ID updated via heartbeat:', userSessionId);
                }
                return;
            }

            // Handle upload status events for sources panel
            if (responseData.type === 'upload_status') {
                const simple = responseData.status || 'info';
                const key = responseData.session_id || responseData.filename;
                if (key) {
                    sourceStatus[key] = simple;
                }
                // Also map by display title if available
                if (responseData.filename) {
                    sourceStatus[responseData.filename] = simple;
                }

                // If status is 'success', remove ephemeral source and refresh from DB
                if (simple === 'success') {
                    // Find and remove ephemeral source by session ID or filename
                    const sessionId = responseData.session_id;
                    const filename = responseData.filename;

                    let epIdx = -1;
                    if (sessionId) {
                        // Try to find by session ID first
                        epIdx = ephemeralSources.findIndex(s => s.sessionId === sessionId);
                    }
                    if (epIdx === -1 && filename) {
                        // Fallback to filename matching
                        epIdx = ephemeralSources.findIndex(s =>
                            s.title === filename ||
                            s.id === filename ||
                            s.title === filename.replace('.yt', '') + '.yt'
                        );
                    }

                    if (epIdx !== -1) {
                        ephemeralSources.splice(epIdx, 1);
                        // Clean up status entries
                        if (filename) delete sourceStatus[filename];
                        if (sessionId) delete sourceStatus[sessionId];
                    }

                    // Refresh sources from database to show the new entry
                    loadDetailedSources();
                }

                // Refresh sources list to reflect updated status (non-intrusive)
                renderSourcesList();
                return;
            }

            // Otherwise it's an AI message
            if (!responseData.message || responseData.message.trim() === '') {
                return;
            }
            hideTypingIndicator();
            addMessageToChat(responseData.message, 'ai');
        } catch (error) {
            console.error('Error parsing SSE data:', error);
        }
    };

    eventSource.onerror = function(event) {
        console.error('SSE connection error:', event);
        // Reconnect after 5 seconds
        setTimeout(() => {
            console.log('Reconnecting SSE...');
            initializeSSE();
        }, 5000);
    };

    eventSource.onopen = function() {
        console.log('SSE connection opened');
    };
}

// UI utility functions
function showStatus(message, type = 'info') {
    statusMessage.textContent = message;
    statusMessage.className = `status-message ${type}`;
    statusMessage.classList.remove('hidden');

    // Auto-hide after 5 seconds for success/info messages
    if (type === 'success' || type === 'info') {
        setTimeout(() => {
            statusMessage.classList.add('hidden');
        }, 5000);
    }
}

function hideStatus() {
    statusMessage.classList.add('hidden');
}

function showLoading(message = 'Processing...') {
    loadingText.textContent = message;
    loadingOverlay.classList.remove('hidden');
}

function hideLoading() {
    loadingOverlay.classList.add('hidden');
}

// Sources Panel Functions
function setupSourcesPanel() {
    // Panel toggle functionality
    panelToggleBtn.addEventListener('click', toggleSourcesPanel);
    mobileSourcesToggle.addEventListener('click', toggleSourcesPanel);
    floatingToggleBtn.addEventListener('click', toggleSourcesPanel);

    // Add source functionality (PDF upload)
    addSourceBtn.addEventListener('click', triggerPdfUpload);
    pdfUploadInput.addEventListener('change', handlePdfUpload);

    // YouTube functionality
    youtubeBtn.addEventListener('click', handleYouTubeTranscript);

    // Removed discover sources functionality

    // Removed select-all functionality
}

// Settings Panel Functions
function setupSettingsPanel() {
    console.log('Setting up settings panel...');

    // Settings panel toggle functionality
    if (settingsToggleBtn) {
        settingsToggleBtn.addEventListener('click', toggleSettingsPanel);
    } else {
        console.error('Settings toggle button not found');
    }

    if (settingsPanelToggle) {
        settingsPanelToggle.addEventListener('click', toggleSettingsPanel);
    } else {
        console.error('Settings panel toggle button not found');
    }

    // External link button functionality
    if (externalLinkBtn) {
        externalLinkBtn.addEventListener('click', openExternalService);
    } else {
        console.error('External link button not found');
    }

    // Font size button functionality
    const fontSizeButtons = document.querySelectorAll('.font-size-btn');
    console.log('Found font size buttons:', fontSizeButtons.length);

    fontSizeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const size = this.getAttribute('data-size');
            console.log('Font size button clicked:', size);
            setFontSize(size);
        });
    });

    // Load saved font size
    loadFontSize();
}

function toggleSettingsPanel() {
    isSettingsPanelCollapsed = !isSettingsPanelCollapsed;
    settingsPanel.classList.toggle('collapsed', isSettingsPanelCollapsed);

    // Update toggle button icon
    const icon = settingsPanelToggle.querySelector('svg path');
    if (isSettingsPanelCollapsed) {
        icon.setAttribute('d', 'M15 18l-6-6 6-6'); // Left arrow
    } else {
        icon.setAttribute('d', 'M9 18l6-6-6-6'); // Right arrow
    }
}

function setFontSize(size) {
    currentFontSize = size;

    // Remove all font size classes
    document.body.classList.remove('font-small', 'font-medium', 'font-large');

    // Add the selected font size class
    document.body.classList.add(`font-${size}`);

    // Update button states
    const fontSizeButtons = document.querySelectorAll('.font-size-btn');
    fontSizeButtons.forEach(button => {
        button.classList.remove('active');
        if (button.getAttribute('data-size') === size) {
            button.classList.add('active');
        }
    });

    // Save to localStorage
    localStorage.setItem('fontSize', size);
}

function loadFontSize() {
    const savedFontSize = localStorage.getItem('fontSize') || 'medium';
    setFontSize(savedFontSize);
}

// External service functionality
function openExternalService() {
    window.open('http://172.27.1.229:5004', '_blank');
}

// Source deletion functionality
async function deleteSource(sourceId) {
    if (!confirm(`Are you sure you want to delete the source "${sourceId}"? This will remove all associated chunks from the database and cannot be undone.`)) {
        return;
    }

    showLoading('Deleting source...');

    try {
        const response = await fetch('/api/sources/delete', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                source_id: sourceId
            })
        });

        const data = await response.json();

        if (response.ok) {
            showStatus(`Successfully deleted source "${sourceId}"`, 'success');
            // Refresh the sources list
            await loadDetailedSources();
        } else {
            showStatus(`Failed to delete source: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Error deleting source:', error);
        showStatus(`Error deleting source: ${error.message}`, 'error');
    } finally {
        hideLoading();
    }
}

function toggleSourcesPanel() {
    isPanelCollapsed = !isPanelCollapsed;
    sourcesPanel.classList.toggle('collapsed', isPanelCollapsed);

    // Update toggle button icon
    const icon = panelToggleBtn.querySelector('svg path');
    if (isPanelCollapsed) {
        icon.setAttribute('d', 'M9 18l6-6-6-6'); // Right arrow
        floatingToggleBtn.classList.remove('hidden'); // Show floating button
    } else {
        icon.setAttribute('d', 'M15 18l-6-6 6-6'); // Left arrow
        floatingToggleBtn.classList.add('hidden'); // Hide floating button
    }
}

async function loadDetailedSources() {
    try {
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 5000);
        const response = await fetch('/api/sources/detailed', { signal: controller.signal, cache: 'no-store' });
        clearTimeout(timeout);
        if (response.ok) {
            const data = await response.json();
            detailedSources = data.sources || [];
            renderSourcesList();
            updateSourcesCount();
            console.log(`Loaded ${detailedSources.length} detailed sources`);
        } else {
            console.warn('Failed to load detailed sources:', response.status);
            showStatus('Warning: Could not load sources list.', 'error');
        }
    } catch (error) {
        console.error('Error loading detailed sources:', error);
        showStatus('Warning: Could not connect to sources database.', 'error');
    }
}

function renderSourcesList() {
    sourcesList.innerHTML = '';

    // Compose a merged list: ephemeral first, then DB-backed
    const merged = [...ephemeralSources, ...detailedSources.filter(ds => !ephemeralSources.find(es => es.id === ds.id))];

    merged.forEach(source => {
        const sourceItem = document.createElement('div');
        sourceItem.className = 'source-item';
        sourceItem.setAttribute('data-source-id', source.id);

        const iconText = getSourceIconText(source.type, source.title);
        const iconClass = source.title && source.title.endsWith('.yt') ? 'youtube' : source.type;

        // Display title without .yt for YouTube entries
        const displayTitle = (source.title && source.title.endsWith('.yt')) ? source.title.slice(0, -3) : source.title;
        // Status badge (simple): reading status from either session or filename keys
        const statusVal = sourceStatus[source.title] || '';
        const statusBadge = statusVal ? `<span class="source-status ${statusVal}">${statusVal}</span>` : '';

        sourceItem.innerHTML = `
            <div class="source-icon ${iconClass}">${iconText}</div>
            <div class="source-text" title="${displayTitle}">
                ${displayTitle}
            </div>
            ${statusBadge}
            <button class="source-delete-btn" data-source-id="${source.id}" title="Delete source">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                    <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14zM10 11v6M14 11v6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        `;

        // Add event listener for delete button
        const deleteBtn = sourceItem.querySelector('.source-delete-btn');
        deleteBtn.addEventListener('click', () => {
            deleteSource(source.id);
        });

        sourcesList.appendChild(sourceItem);
    });
}

function getSourceIconText(type, title) {
    // Check if title ends with .yt for YouTube sources
    if (title && title.endsWith('.yt')) {
        return '▶';
    }

    switch (type) {
        case 'youtube':
            return '▶';
        case 'text':
            return 'T';
        case 'pdf':
            return '📄';
        default:
            return '📄';
    }
}

// Removed checkbox functionality - sources are now display-only

function updateSourcesCount() {
    const totalCount = detailedSources.length;
    sourcesCount.textContent = `${totalCount} sources available`;
}

function triggerPdfUpload() {
    pdfUploadInput.click();
}

async function handlePdfUpload(event) {
    const fileInput = event.target;
    const file = fileInput.files[0];

    if (!file) return;

    // Validate file type (client-side check)
    if (!file.name.toLowerCase().endsWith('.pdf')) {
        showStatus('Please select a valid PDF file', 'error');
        return;
    }

    // Proactively show status in sources panel for this new file
    try {
        // Add to ephemeral list immediately so it shows before DB save
        if (!ephemeralSources.find(s => s.title === file.name)) {
            ephemeralSources.unshift({ id: file.name, title: file.name, type: 'pdf', document_count: 0, ephemeral: true });
        }
        // Also add a visual status
        sourceStatus[file.name] = 'processing';
        renderSourcesList();
    } catch (e) { /* no-op */ }

    // Show loading state
    showLoading('Uploading and processing PDF...');

    try {
        // Create FormData and send to the existing PDF processing service
        const formData = new FormData();
        formData.append('pdfFile', file);

        const response = await fetch('http://localhost:5555/upload-pdf', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (response.ok && result.status === 'success') {
            let message = `Success! PDF processed successfully. Extracted ${result.elements_count || 'content'} elements.`;

            if (result.workflow_triggered) {
                message += '.';
            } else {
                message += ' Warning: n8n workflow could not be triggered - please check if n8n is running.';
            }

            // Refresh sources from ChromaDB to get the actual uploaded data
            await loadDetailedSources();
            // Remove ephemeral entry now that it should be in DB
            const epIdx = ephemeralSources.findIndex(s => s.title === file.name);
            if (epIdx !== -1) ephemeralSources.splice(epIdx, 1);
            showStatus(message, 'success');

            // Reset form
            fileInput.value = '';
        } else {
            showStatus(`Error: ${result.message || 'Unknown error occurred'}`, 'error');
        }

    } catch (error) {
        console.error('Upload error:', error);
        showStatus('Error: Failed to connect to server. Please check if the service is running.', 'error');
    } finally {
        hideLoading();
    }
}

// YouTube transcript functionality
async function handleYouTubeTranscript() {
    const youtubeUrl = prompt('Please paste a YouTube video URL to extract transcript:');

    if (!youtubeUrl) {
        return; // User cancelled
    }

    // Basic YouTube URL validation
    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)/;
    if (!youtubeRegex.test(youtubeUrl)) {
        showStatus('Please enter a valid YouTube URL.', 'error');
        return;
    }

    // Extract a temporary title from URL for immediate display
    let tempTitle = 'YouTube Video';
    try {
        const url = new URL(youtubeUrl);
        if (url.hostname.includes('youtube.com')) {
            const videoId = url.searchParams.get('v');
            if (videoId) tempTitle = `YouTube Video (${videoId})`;
        } else if (url.hostname.includes('youtu.be')) {
            const videoId = url.pathname.slice(1);
            if (videoId) tempTitle = `YouTube Video (${videoId})`;
        }
    } catch (e) {
        // Use default title if URL parsing fails
    }

    // Add .yt extension for consistency
    const tempTitleWithExt = tempTitle + '.yt';

    // Extract video ID for session tracking
    let videoId = null;
    try {
        const url = new URL(youtubeUrl);
        if (url.hostname.includes('youtube.com')) {
            videoId = url.searchParams.get('v');
        } else if (url.hostname.includes('youtu.be')) {
            videoId = url.pathname.substring(1);
        }
    } catch (e) { /* no-op */ }

    const sessionId = videoId ? `youtube_${videoId}` : null;

    // Proactively show status in sources panel for this new YouTube video
    try {
        // Add to ephemeral list immediately so it shows before processing
        if (!ephemeralSources.find(s => s.title === tempTitleWithExt)) {
            ephemeralSources.unshift({
                id: tempTitleWithExt,
                title: tempTitleWithExt,
                type: 'youtube',
                document_count: 0,
                ephemeral: true,
                sessionId: sessionId  // Track session ID
            });
        }
        // Also add a visual status
        sourceStatus[tempTitleWithExt] = 'processing';
        if (sessionId) {
            sourceStatus[sessionId] = 'processing';
        }
        renderSourcesList();
    } catch (e) { /* no-op */ }

    showLoading('Extracting YouTube transcript...');

    try {
        const response = await fetch('/api/extract-youtube-transcript', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                youtube_url: youtubeUrl
            })
        });

        const data = await response.json();

        if (response.ok) {
            // Update the ephemeral entry with the actual video title but keep it visible
            const epIdx = ephemeralSources.findIndex(s => s.title === tempTitleWithExt);
            if (epIdx !== -1) {
                // Update with actual video title
                const actualTitle = (data.video_title || 'YouTube Video') + '.yt';
                ephemeralSources[epIdx].title = actualTitle;
                ephemeralSources[epIdx].id = actualTitle;

                // Update status to show it's being processed by n8n
                sourceStatus[actualTitle] = 'processing';
                delete sourceStatus[tempTitleWithExt]; // Remove old status

                // Keep session ID tracking
                if (sessionId) {
                    sourceStatus[sessionId] = 'processing';
                }
            }

            showStatus(`Successfully extracted transcript from "${data.video_title || 'YouTube Video'}". Processing...`, 'info');
            renderSourcesList(); // Update the display with new title

            // Note: The ephemeral source will be removed when we receive 'success' upload status via SSE
        } else {
            // Remove the temporary ephemeral entry on error
            const epIdx = ephemeralSources.findIndex(s => s.title === tempTitleWithExt);
            if (epIdx !== -1) ephemeralSources.splice(epIdx, 1);

            // Clear temporary status
            delete sourceStatus[tempTitleWithExt];
            if (sessionId) {
                delete sourceStatus[sessionId];
            }

            showStatus(`Failed to extract transcript: ${data.error}`, 'error');
        }
    } catch (error) {
        console.error('Error extracting YouTube transcript:', error);

        // Remove the temporary ephemeral entry on error
        const epIdx = ephemeralSources.findIndex(s => s.title === tempTitleWithExt);
        if (epIdx !== -1) ephemeralSources.splice(epIdx, 1);

        // Clear temporary status
        delete sourceStatus[tempTitleWithExt];
        if (sessionId) {
            delete sourceStatus[sessionId];
        }

        showStatus(`Error extracting transcript: ${error.message}`, 'error');
    } finally {
        hideLoading();
        // Ensure UI is updated even if there were errors
        renderSourcesList();
    }
}

// Removed getSelectedSources function - no longer needed without checkboxes

// Test function to add multiple messages for testing scrolling behavior
function addTestMessages() {
    for (let i = 1; i <= 10; i++) {
        addMessageToChat(`This is test user message ${i}. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.`, 'user');
        addMessageToChat(`This is test AI response ${i}. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.`, 'ai');
    }
}

// Add test messages function to window for console access
window.addTestMessages = addTestMessages;

// Error handling for unhandled promise rejections
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection:', event.reason);
    showStatus('An unexpected error occurred. Please try again.', 'error');
});
