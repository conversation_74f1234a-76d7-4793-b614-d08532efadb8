import os
import uuid
from zep_cloud.client import Zep

API_KEY = 'z_1dWlkIjoiYjE4NGU1YmItYTAzNC00YWFjLWFkYTUtNjQwOWQ4MjYzMWVmIn0.rp_YIRMHt24qTSw6J4Tx-ia9BNsC8mEmxgc0gilyJAvRac6fKtDrD3-MqrH5KZa09ETdm58O9Be4app2SbG-Ng'

client = Zep(
    api_key=API_KEY,
)

user_id = "n8nmemory"
new_user = client.user.add(
    user_id=user_id,
    email="<EMAIL>",
    first_name="RS",
    last_name="SY",
)

# Generate a unique session ID
session_id = uuid.uuid4().hex

# Create a new session for the user
client.memory.add_session(
    session_id=session_id,
    user_id=user_id,
)
