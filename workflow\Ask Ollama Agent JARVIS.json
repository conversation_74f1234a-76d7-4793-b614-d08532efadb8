{"name": "Ask Ollama Agent JARVIS", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [784, 144], "id": "4e0b3127-966b-47c3-a1f2-89b0c9834b63", "name": "Query ChromaDB"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.1, "position": [976, 144], "id": "acb81c57-5de9-437b-ae8f-d0b996f0d1b9", "name": "AI Agent"}, {"parameters": {}, "id": "e88b003a-42c4-4749-91e8-fbc881866d7d", "name": "Get Embeddings1", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [592, 144]}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [976, 336], "id": "e39025c0-1522-45f9-8b3d-9769ce67ab37", "name": "<PERSON><PERSON><PERSON> Chat Model", "credentials": {"ollamaApi": {"id": "yEnEIhPEbVPuZH4a", "name": "Ollama account"}}}, {"parameters": {}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1344, 144], "id": "1a72d32e-9fed-49dc-b509-5d28f03cafe9", "name": "Format Ollama Output"}, {"parameters": {}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [416, 144], "id": "e7003a77-3d66-4611-a32c-a2cd0d666ba6", "name": "Source Filter"}, {"parameters": {}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 144], "id": "f765cb7c-73ff-47f6-835e-48bf23d767ba", "name": "Webhook", "webhookId": "a9d20474-dea8-4c13-8b3b-14aa71847780"}, {"parameters": {}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1536, 144], "id": "6ac10b6c-3283-4752-9d28-df5e62b72619", "name": "HTTP Request"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [1104, 352], "id": "c8225ec1-9799-4bb8-87b9-fba476f12218", "name": "Simple Memory"}], "pinData": {}, "connections": {"Query ChromaDB": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Format Ollama Output", "type": "main", "index": 0}]]}, "Get Embeddings1": {"main": [[{"node": "Query ChromaDB", "type": "main", "index": 0}]]}, "Ollama Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Format Ollama Output": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Source Filter": {"main": [[{"node": "Get Embeddings1", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Source Filter", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "ea43da1f-11e9-41a0-a85d-a90caaa12a8a", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e03cb1825228004b62e0a6d97f886e4ed3c55f131de25c4d61250f2f6671512a"}, "id": "HKq24LUc3wcsYWvo", "tags": []}