/* Gemini-inspired Dark Theme Color Scheme */
:root {
    --primary-bg: #131314;
    --secondary-bg: #1e1f20;
    --surface-bg: #2f3030;
    --surface-hover: #3c4043;
    --border-color: #3c4043;
    --text-primary: #e8eaed;
    --text-secondary: #9aa0a6;
    --text-muted: #5f6368;
    --accent-blue: #8ab4f8;
    --accent-green: #81c995;
    --accent-red: #f28b82;
    --accent-yellow: #fdd663;
    --input-bg: #303134;
    --input-border: #5f6368;
    --input-focus: #8ab4f8;
    --message-user-bg: #8ab4f8;
    --message-ai-bg: #303134;
    --shadow: rgba(0, 0, 0, 0.3);

    /* Font size variables */
    --font-size-small: 18px;
    --font-size-medium: 20px;
    --font-size-large: 22px;
    --current-font-size: var(--font-size-medium);
}

/* Font size classes */
.font-small {
    --current-font-size: var(--font-size-small);
}

.font-medium {
    --current-font-size: var(--font-size-medium);
}

.font-large {
    --current-font-size: var(--font-size-large);
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Google Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--primary-bg);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
}

/* App Layout */
.app-container {
    display: flex;
    height: 100vh;
    max-width: 100vw;
    overflow: hidden;
}

/* Sources Panel */
.sources-panel {
    width: 350px;
    min-width: 350px;
    background-color: var(--secondary-bg);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: width 0.3s ease, min-width 0.3s ease;
    height: 100vh;
}

.sources-panel.collapsed {
    width: 0;
    min-width: 0;
    border-right: none;
}

.sources-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background-color: var(--secondary-bg);
}

.sources-title {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-primary);
}

.panel-toggle-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.panel-toggle-btn:hover {
    background-color: var(--surface-hover);
    color: var(--text-primary);
}

.settings-toggle-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

/* Simple status badge for sources */
.source-status {
    display: inline-block;
    margin-left: 8px;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    line-height: 1;
    background-color: var(--border-color);
    color: var(--text-secondary);
}
.source-status.started, .source-status.processing {
    background-color: #e9f0ff;
    color: #2f6feb;
}
.source-status.completed {
    background-color: #e6f4ea;
    color: #137333;
}
.source-status.error {
    background-color: #fce8e6;
    color: #c5221f;
}

.settings-toggle-btn:hover {
    background-color: var(--surface-hover);
    color: var(--text-primary);
}

.external-link-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.external-link-btn:hover {
    background-color: var(--surface-hover);
    color: var(--text-primary);
}

.sources-actions {
    display: flex;
    gap: 8px;
    padding: 16px 20px;
}

.action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 12px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-secondary);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: var(--surface-hover);
    color: var(--text-primary);
    border-color: var(--input-focus);
}

.action-btn.youtube-btn {
    background-color: #ff0000;
    color: white;
    border-color: #ff0000;
}

.action-btn.youtube-btn:hover {
    background-color: #cc0000;
    border-color: #cc0000;
    color: white;
}

/* Removed select-all checkbox styles */

.sources-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
}

.source-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 20px;
    transition: background-color 0.2s ease;
    position: relative;
}

.source-item:hover {
    background-color: var(--surface-hover);
}

.source-item:hover .source-delete-btn {
    opacity: 1;
}

/* Removed source-checkbox styles */

.source-icon {
    width: 20px;
    height: 20px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    font-weight: bold;
    flex-shrink: 0;
}

.source-icon.doc {
    background-color: var(--accent-blue);
}

.source-icon.youtube {
    background-color: var(--accent-red);
}

.source-icon.text {
    background-color: var(--text-muted);
}

.source-icon.pdf {
    background-color: var(--accent-red);
}

.source-text {
    flex: 1;
    font-size: 14px;
    color: var(--text-primary);
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

.source-delete-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: transparent;
    border: none;
    border-radius: 4px;
    color: var(--text-muted);
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.source-delete-btn:hover {
    background-color: var(--accent-red);
    color: white;
    opacity: 1;
}

.sources-footer {
    padding: 12px 20px;
    background-color: var(--secondary-bg);
}

.sources-count {
    font-size: 12px;
    color: var(--text-muted);
}

/* Settings Panel */
.settings-panel {
    width: 350px;
    min-width: 350px;
    background-color: var(--secondary-bg);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: transform 0.3s ease;
    height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1000;
    transform: translateX(0);
}

.settings-panel.collapsed {
    transform: translateX(100%);
}

.settings-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background-color: var(--secondary-bg);
}

.settings-title {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-primary);
}

.settings-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.setting-group {
    margin-bottom: 24px;
}

.setting-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.font-size-buttons {
    display: flex;
    background-color: #2a2a2a;
    border-radius: 12px;
    padding: 4px;
    position: relative;
}

.font-size-btn {
    flex: 1;
    padding: 8px 16px;
    background: transparent;
    border: none;
    border-radius: 12px;
    color: var(--text-muted);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    z-index: 1;
    text-align: center;
}

.font-size-btn:hover:not(.active) {
    color: var(--text-secondary);
}

.font-size-btn.active {
    background-color: #404040;
    color: var(--text-primary);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Main Content Area */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    height: 100vh;
    overflow: hidden;
}

/* Header */
.app-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    background-color: var(--primary-bg);
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.app-title {
    font-size: 22px;
    font-weight: 400;
    color: var(--text-primary);
    margin: 0;
}

.model-selector {
    position: relative;
}

.model-dropdown {
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: 8px;
    color: var(--text-primary);
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    outline: none;
    transition: all 0.2s ease;
}

.model-dropdown:hover {
    border-color: var(--input-focus);
}

.model-dropdown:focus {
    border-color: var(--input-focus);
    box-shadow: 0 0 0 2px rgba(138, 180, 248, 0.2);
}

.mobile-sources-toggle {
    display: none;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.mobile-sources-toggle:hover {
    background-color: var(--surface-hover);
    color: var(--text-primary);
}

/* Main Chat Area */
.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 0 24px;
    min-height: 0;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: 768px;
    margin: 0 auto;
    width: 100%;
    overflow-y: auto;
    padding: 24px 0;
    gap: 24px;
    min-height: 0;
    height: 100%;
}

/* Welcome Section */
.welcome-section {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    min-height: 400px;
}

.welcome-message {
    text-align: center;
    max-width: 600px;
}

.welcome-message h2 {
    font-size: 48px;
    font-weight: 400;
    color: var(--text-primary);
    margin-bottom: 16px;
    background: linear-gradient(45deg, var(--accent-blue), var(--accent-green));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-message p {
    font-size: 20px;
    color: var(--text-secondary);
    font-weight: 300;
}

/* Input Footer */
.input-footer {
    padding: 24px;
    background-color: var(--primary-bg);
    border-top: 1px solid var(--border-color);
    flex-shrink: 0;
}

.input-container {
    max-width: 768px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    gap: 12px;
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: 24px;
    padding: 16px 18px;
    transition: all 0.2s ease;
}

.input-container:focus-within {
    border-color: var(--input-focus);
    box-shadow: 0 0 0 2px rgba(138, 180, 248, 0.2);
}

.input-wrapper {
    flex: 1;
    position: relative;
}

.message-input {
    width: 100%;
    background: transparent;
    border: none;
    outline: none;
    color: var(--text-primary);
    font-size: var(--current-font-size);
    line-height: 26px;
    padding: 0;
}

.message-input::placeholder {
    color: var(--text-muted);
}

.mic-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.mic-button:hover {
    background-color: var(--surface-hover);
    color: var(--text-primary);
}

.mic-button:active, .mic-button.recording {
    background-color: var(--accent-red);
    color: white;
}

/* Messages */
.message {
    display: flex;
    flex-direction: column;
    max-width: 80%;
    margin-bottom: 24px;
    animation: fadeIn 0.3s ease-in;
}

.user-message {
    align-self: flex-end;
}

.ai-message {
    align-self: flex-start;
}

.message-content {
    padding: 16px 20px;
    border-radius: 18px;
    word-wrap: break-word;
    line-height: 1.5;
    font-size: var(--current-font-size);
}

.user-message .message-content {
    background: var(--message-user-bg);
    color: var(--primary-bg);
    border-bottom-right-radius: 4px;
}

.ai-message .message-content {
    background: var(--message-ai-bg);
    color: var(--text-primary);
    border-bottom-left-radius: 4px;
    border: 1px solid var(--border-color);
}

.message-time {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: 8px;
    padding: 0 8px;
}

.user-message .message-time {
    text-align: right;
}

.ai-message .message-time {
    text-align: left;
}

/* Autocomplete dropdown */
.autocomplete-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--surface-bg);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 12px var(--shadow);
    transition: all 0.2s ease;
}

/* Autocomplete dropdown when expanding upward */
.autocomplete-dropdown.expand-up {
    top: auto;
    bottom: 100%;
    border-top: 1px solid var(--border-color);
    border-bottom: none;
    border-radius: 8px 8px 0 0;
    box-shadow: 0 -4px 12px var(--shadow);
}

.autocomplete-item {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
    color: var(--text-primary);
}

.autocomplete-item:hover, .autocomplete-item.selected {
    background: var(--surface-hover);
}

.autocomplete-item:last-child {
    border-bottom: none;
}

/* Recording indicator */
.recording-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 12px 16px;
    background: var(--accent-red);
    color: white;
    border-radius: 8px;
    font-weight: 500;
    margin-top: 12px;
    max-width: 768px;
    margin-left: auto;
    margin-right: auto;
}

.recording-animation {
    position: relative;
    width: 16px;
    height: 16px;
}

.pulse {
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        opacity: 1;
    }
    70% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(0.95);
        opacity: 1;
    }
}

/* Status messages */
.status-message {
    padding: 12px 16px;
    border-radius: 8px;
    font-weight: 500;
    text-align: center;
    margin-top: 12px;
    max-width: 768px;
    margin-left: auto;
    margin-right: auto;
}

.status-message.success {
    background: rgba(129, 201, 149, 0.1);
    color: var(--accent-green);
    border: 1px solid rgba(129, 201, 149, 0.3);
}

.status-message.error {
    background: rgba(242, 139, 130, 0.1);
    color: var(--accent-red);
    border: 1px solid rgba(242, 139, 130, 0.3);
}

.status-message.info {
    background: rgba(138, 180, 248, 0.1);
    color: var(--accent-blue);
    border: 1px solid rgba(138, 180, 248, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sources-panel {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sources-panel:not(.collapsed) {
        transform: translateX(0);
    }

    .sources-panel.collapsed {
        transform: translateX(-100%);
    }

    .main-content {
        width: 100%;
    }

    .app-header {
        padding: 12px 16px;
    }

    .app-title {
        font-size: 18px;
    }

    .mobile-sources-toggle {
        display: flex;
    }

    .chat-main {
        padding: 0 16px;
    }

    .input-footer {
        padding: 16px;
    }

    .settings-panel {
        width: 280px;
        min-width: 280px;
    }
}

@media (max-width: 480px) {
    .sources-panel {
        width: 280px;
        min-width: 280px;
    }

    .sources-actions {
        flex-direction: column;
    }

    .action-btn {
        flex: none;
    }
}

/* Enhanced formatting for AI responses */
.message-content strong {
    font-weight: 600;
}

.message-content em {
    font-style: italic;
    opacity: 0.9;
}

.message-content code {
    background: var(--surface-bg);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 0.9em;
    border: 1px solid var(--border-color);
}

.message-content pre {
    background: var(--surface-bg);
    padding: 12px;
    border-radius: 8px;
    overflow-x: auto;
    border: 1px solid var(--border-color);
    margin: 8px 0;
}

.message-content pre code {
    background: none;
    padding: 0;
    border: none;
}

/* Typing indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 20px;
    background: var(--message-ai-bg);
    border: 1px solid var(--border-color);
    border-radius: 18px;
    border-bottom-left-radius: 4px;
    max-width: 80px;
    align-self: flex-start;
    margin-bottom: 24px;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background: var(--text-secondary);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

.loading-spinner {
    background: var(--surface-bg);
    padding: 32px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 8px 32px var(--shadow);
    border: 1px solid var(--border-color);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--accent-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Floating Toggle Button */
.floating-toggle-btn {
    position: fixed;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background: transparent;
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

.floating-toggle-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-50%) scale(1.05);
}

.floating-toggle-btn svg {
    color: white;
}

/* Hide scrollbars */
.chat-container::-webkit-scrollbar,
.sources-list::-webkit-scrollbar,
.autocomplete-dropdown::-webkit-scrollbar,
.message-content pre::-webkit-scrollbar {
    width: 0px;
    background: transparent;
}

.chat-container::-webkit-scrollbar-thumb,
.sources-list::-webkit-scrollbar-thumb,
.autocomplete-dropdown::-webkit-scrollbar-thumb,
.message-content pre::-webkit-scrollbar-thumb {
    background: transparent;
}

/* For Firefox */
.chat-container,
.sources-list,
.autocomplete-dropdown,
.message-content pre {
    scrollbar-width: none;
}

/* Utility classes */
.hidden {
    display: none !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .app-header {
        padding: 12px 16px;
    }

    .app-title {
        font-size: 20px;
    }

    .chat-main {
        padding: 0 16px;
    }

    .input-footer {
        padding: 16px;
    }

    .welcome-message h2 {
        font-size: 36px;
    }

    .welcome-message p {
        font-size: 18px;
    }

    .message {
        max-width: 90%;
    }

    .message-content {
        padding: 12px 16px;
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .header-left {
        gap: 12px;
    }

    .model-dropdown {
        font-size: 13px;
        padding: 6px 10px;
    }

    .welcome-message h2 {
        font-size: 28px;
    }

    .welcome-message p {
        font-size: 16px;
    }
}
