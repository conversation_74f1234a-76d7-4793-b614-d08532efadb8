"""
YouTube link enqueue endpoint for deferred transcript extraction.
"""

from flask import request, jsonify
from ..core.utils import create_error_response
import http.client
import json
from ..flows.process_queue import queue_manager, ProcessType
from ..core.config import RAPIDAPI_KEY

# Helper to extract transcript when a YouTube deferred job becomes ACTIVE
# Reuses the RapidAPI logic currently in Question_Page
RAPID_API_HOST = "youtube-transcriptor.p.rapidapi.com"


def _extract_youtube_transcript_if_needed(process, logger):
    """If process is a deferred YouTube job, fetch transcript text and attach to process.content.
    Returns True on success or if not applicable; False on failure.
    """
    try:
        if not process.metadata.get("deferred_youtube"):
            return True
        video_url = process.metadata.get("video_url")
        if not video_url:
            return False
        # Extract video id from URL (simple parsing equivalent)
        from urllib.parse import urlparse, parse_qs
        parsed = urlparse(video_url)
        host = (parsed.hostname or "").lower()
        video_id = None
        if host == "youtu.be":
            video_id = parsed.path.lstrip("/") or None
        elif host in ("www.youtube.com", "youtube.com", "m.youtube.com"):
            video_id = parse_qs(parsed.query).get("v", [None])[0]
        if not video_id:
            video_id = video_url if len(video_url) >= 8 else None
        if not video_id:
            return False

        conn = http.client.HTTPSConnection(RAPID_API_HOST)
        headers = {
            'x-rapidapi-key': process.metadata.get('rapidapi_key') or "",
            'x-rapidapi-host': RAPID_API_HOST
        }
        endpoint = f"/transcript?video_id={video_id}&lang=en"
        conn.request("GET", endpoint, headers=headers)
        res = conn.getresponse()
        data = res.read()
        if res.status != 200:
            return False
        payload = json.loads(data.decode("utf-8"))
        # Extract transcript text similar to Question_Page
        transcript_text = None
        data_obj = payload[0] if isinstance(payload, list) and payload else payload
        # Try direct strings
        for key in ("transcriptionAsText", "text"):
            if isinstance(data_obj.get(key), str) and data_obj[key].strip():
                transcript_text = data_obj[key]
                break
        # Try arrays
        if transcript_text is None:
            parts = []
            for key in ("transcription", "transcript"):
                if isinstance(data_obj.get(key), list) and data_obj[key]:
                    for seg in data_obj[key]:
                        seg_text = (seg.get("subtitle") or seg.get("text") or "").strip()
                        if seg_text:
                            parts.append(seg_text)
            if parts:
                transcript_text = " ".join(parts)
        if not transcript_text:
            return False
        # Clean minimal
        import re
        cleaned = re.sub(r"\s+", " ", transcript_text).strip()
        process.content = cleaned
        return True
    except Exception:
        return False

from ..core.state import state_manager, UploadStatus
from ..core.utils import send_upload_status_notification


def enqueue_youtube_link(app):
    """Accept a YouTube URL and title, enqueue for deferred extraction and processing.

    Expected JSON: { "video_url": str, "video_title": str, "source": str }
    """
    try:
        if not request.is_json:
            return create_error_response("Request must be JSON", 400)

        data = request.get_json() or {}
        video_url = (data.get("video_url") or "").strip()
        video_title = (data.get("video_title") or "YouTube Video").strip()
        source = (data.get("source") or "youtube_transcripts").strip()

        if not video_url:
            return create_error_response("video_url is required", 400)

        # Normalize title to include .yt exactly once
        if not video_title.lower().endswith(".yt"):
            video_title = video_title + ".yt"

        # Enqueue link-only job
        session_id, started_immediately = queue_manager.add_to_queue(
            process_type=ProcessType.YOUTUBE,
            filename=video_title,
            content="",  # defer transcript text
            metadata={
                "source": source,
                "video_url": video_url,
                "processing_method": "youtube_link_deferred",
                "deferred_youtube": True,
                "rapidapi_key": RAPIDAPI_KEY,
            }
        )

        # State session + initial status
        state_manager.create_session(
            session_id=session_id,
            filename=video_title,
            upload_type="youtube",
            metadata={
                "source": source,
                "video_url": video_url,
            }
        )

        if started_immediately:
            state_manager.update_session_status(session_id, UploadStatus.ACTIVE, "Processing started")
            try:
                send_upload_status_notification({
                    "endpoint_type": "youtube",
                    "status": "processing",
                    "filename": video_title,
                    "session_id": session_id,
                    "message": f"Started processing {video_title}"
                })
            except Exception:
                pass
            message = "YouTube link accepted and processing started"
            queue_status = "started"
        else:
            queue_status_info = queue_manager.get_queue_status()
            queue_position = len(queue_status_info.get("pending_queue", []))
            state_manager.update_queue_position(session_id, queue_position)
            try:
                send_upload_status_notification({
                    "endpoint_type": "youtube",
                    "status": "queued",
                    "filename": video_title,
                    "session_id": session_id,
                    "message": f"Queued at position {queue_position}"
                })
            except Exception:
                pass
            message = f"YouTube link accepted and queued at position {queue_position}"
            queue_status = "queued"

        return jsonify({
            "status": "success",
            "message": message,
            "data": {
                "video_title": video_title,
                "video_url": video_url,
                "source": source,
                "queue_status": queue_status,
                "session_id": session_id,
            }
        }), 200
    except Exception as e:
        return create_error_response(f"Unexpected error: {e}", 500)

