<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Login • AI Assistant</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
  <style>
    /* Login page specific styles extending existing theme */
    .login-page {
      display: flex;
      min-height: 100vh;
      background: var(--primary-bg);
      color: var(--text-primary);
    }
    .login-left, .login-right {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px;
    }
    .login-left {
      background: var(--primary-bg);
    }
    .login-card {
      width: 100%;
      max-width: 420px;
      background: var(--secondary-bg);
      border: 1px solid var(--border-color);
      border-radius: 16px;
      padding: 28px;
      box-shadow: 0 8px 32px var(--shadow);
    }
    .login-title {
      font-size: 24px;
      margin-bottom: 8px;
    }
    .login-subtitle {
      color: var(--text-secondary);
      margin-bottom: 24px;
    }
    .form-group { margin-bottom: 16px; }
    .form-label {
      display: block;
      font-size: 14px;
      margin-bottom: 8px;
      color: var(--text-secondary);
    }
    .form-input {
      width: 100%;
      padding: 12px 14px;
      background: var(--input-bg);
      color: var(--text-primary);
      border: 1px solid var(--input-border);
      border-radius: 10px;
      outline: none;
      transition: border-color .2s ease, box-shadow .2s ease;
    }
    .form-input:focus {
      border-color: var(--input-focus);
      box-shadow: 0 0 0 2px rgba(138, 180, 248, 0.2);
    }
    .login-actions { margin-top: 20px; }
    .btn-primary {
      width: 100%;
      padding: 12px 16px;
      border: none;
      border-radius: 12px;
      background: linear-gradient(45deg, var(--accent-blue), var(--accent-green));
      color: #000;
      font-weight: 600;
      cursor: pointer;
      transition: transform .1s ease;
    }
    .btn-primary:active { transform: translateY(1px); }
    .login-meta {
      margin-top: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: var(--text-secondary);
      font-size: 13px;
    }
    .error-banner {
      background: rgba(242, 139, 130, 0.1);
      color: var(--accent-red);
      border: 1px solid rgba(242, 139, 130, 0.3);
      padding: 10px 12px;
      border-radius: 10px;
      margin-bottom: 16px;
    }

    /* Right solar system animation */
    .login-right { position: relative; overflow: hidden; }
    .solar-wrapper {
      width: 520px; height: 520px; position: relative;
      border-radius: 50%;
      opacity: 0.9;
      filter: drop-shadow(0 20px 60px rgba(0,0,0,0.5));
    }
    .solar-wrapper::before {
      content: ""; position: absolute; inset: 0; pointer-events: none;
      background:
        radial-gradient(2px 2px at 20% 30%, rgba(255,255,255,0.15), transparent 45%),
        radial-gradient(2px 2px at 70% 20%, rgba(255,255,255,0.12), transparent 45%),
        radial-gradient(2px 2px at 30% 75%, rgba(255,255,255,0.10), transparent 45%),
        radial-gradient(1.5px 1.5px at 85% 65%, rgba(255,255,255,0.13), transparent 45%),
        radial-gradient(1.5px 1.5px at 45% 50%, rgba(255,255,255,0.12), transparent 45%);
    }
    .sun {
      position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
      width: 90px; height: 90px; border-radius: 50%;
      background: radial-gradient(circle at 30% 30%, #ffec99, #fdd663, #f28b82);
      box-shadow: 0 0 40px #fdd663, 0 0 80px rgba(253,214,99,0.5);
      z-index: 2;
    }

    /* Orbit structure: a static ring + a rotating carrier for revolution */
    .orbit { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); }
    .ring { position: absolute; inset: 0; border: 1px dashed rgba(255,255,255,0.08); border-radius: 50%; }
    .rotator { position: absolute; inset: 0; transform-origin: 50% 50%; animation: spin var(--dur, 12s) linear infinite; }
    .rotator { transform: rotate(var(--start, 0deg)); }
    .planet { position: absolute; top: 50%; left: 50%; width: var(--size, 12px); height: var(--size, 12px); border-radius: 50%; transform: translate(-50%, -50%) translateX(var(--r)); }

    @keyframes spin { to { transform: rotate(calc(var(--start, 0deg) + 360deg)); } }

    /* Responsive: hide right panel on small screens */
    @media (max-width: 900px) {
      .login-right { display: none; }
      .login-left { flex: 1 1 100%; }
    }
  </style>
</head>
<body>
  <div class="login-page">
    <div class="login-left">
      <form class="login-card" method="POST" action="{{ url_for('login', next=request.args.get('next')) }}">
        <h1 class="login-title">Welcome back</h1>
        <div class="login-subtitle">Sign in to continue to AI Assistant</div>
        {% if error %}
          <div class="error-banner">{{ error }}</div>
        {% endif %}
        <div class="form-group">
          <label class="form-label" for="username">Username</label>
          <input class="form-input" id="username" name="username" placeholder="Enter username" autocomplete="username" required />
        </div>
        <div class="form-group">
          <label class="form-label" for="password">Password</label>
          <input class="form-input" type="password" id="password" name="password" placeholder="Enter password" autocomplete="current-password" required />
        </div>
        <div class="login-actions">
          <button class="btn-primary" type="submit">Sign in</button>
        </div>
        <div class="login-meta">
          <span>Need access? Contact admin.</span>
        </div>
      </form>
    </div>
    <div class="login-right">
      <!-- Minimal solar system animation -->
      <div class="solar-wrapper">
        <div class="sun"></div>

        <!-- Orbit 1 -->
        <div class="orbit" style="width: 140px; height: 140px;">
          <div class="ring"></div>
          <div class="rotator" style="--dur: 16s; --start: 15deg;">
            <div class="planet" style="--r: 70px; --size: 8px; background:#9aa0a6;"></div>
          </div>
        </div>

        <!-- Orbit 2 -->
        <div class="orbit" style="width: 220px; height: 220px;">
          <div class="ring"></div>
          <div class="rotator" style="--dur: 24s; --start: 85deg;">
            <div class="planet" style="--r: 110px; --size: 10px; background:#8ab4f8;"></div>
          </div>
        </div>

        <!-- Orbit 3 -->
        <div class="orbit" style="width: 320px; height: 320px;">
          <div class="ring"></div>
          <div class="rotator" style="--dur: 36s; --start: 220deg;">
            <div class="planet" style="--r: 160px; --size: 14px; background:#81c995;"></div>
          </div>
        </div>

        <!-- Orbit 4 -->
        <div class="orbit" style="width: 420px; height: 420px;">
          <div class="ring"></div>
          <div class="rotator" style="--dur: 56s; --start: 300deg;">
            <div class="planet" style="--r: 210px; --size: 18px; background:#f28b82;"></div>
          </div>
        </div>

        <!-- A few tiny random stars orbiting quickly -->
        <div class="orbit" style="width: 460px; height: 460px;">
          <div class="rotator" style="--dur: 20s; --start: 40deg;">
            <div class="planet" style="--r: 230px; --size: 4px; background:rgba(255,255,255,0.8);"></div>
          </div>
        </div>
        <div class="orbit" style="width: 380px; height: 380px;">
          <div class="rotator" style="--dur: 14s; --start: 200deg;">
            <div class="planet" style="--r: 190px; --size: 3px; background:rgba(255,255,255,0.7);"></div>
          </div>
        </div>
        <div class="orbit" style="width: 260px; height: 260px;">
          <div class="rotator" style="--dur: 10s; --start: 120deg;">
            <div class="planet" style="--r: 130px; --size: 3px; background:rgba(255,255,255,0.7);"></div>
          </div>
        </div>

      </div>
    </div>
  </div>
</body>
</html>

