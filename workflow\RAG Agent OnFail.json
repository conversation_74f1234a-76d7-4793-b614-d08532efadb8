{"name": "RAG Agent OnFail", "nodes": [{"parameters": {"requestMethod": "POST", "url": "http://host.docker.internal:11434/api/embeddings", "jsonParameters": true, "options": {"bodyContentType": "json"}, "bodyParametersJson": "{\n  \"model\": \"snowflake-artic-embed2:568m\",\n  \"prompt\": \"$('AI Agent').item.json.output\"\n}\n", "headerParametersJson": "{\"Content-Type\":\"application/json\"}"}, "id": "b95361a0-0e5d-49ea-95ba-e6cb38caa9b0", "name": "Get Embeddings1", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [2016, 80]}, {"parameters": {"values": {"string": [{"name": "ids", "value": "={{\n  (() => {\n    const now = new Date(new Date().toLocaleString(\"en-US\", { timeZone: \"Asia/Kuala_Lumpur\" }));\n    const yy = now.getFullYear().toString().slice(-2);  // 25\n    const hh = now.getHours().toString().padStart(2, '0'); // 10\n    const mm = (now.getMonth()+1).toString().padStart(2, '0'); // 07 (Jan=0)\n    const mi = now.getMinutes().toString().padStart(2, '0'); // 40\n    const dd = now.getDate().toString().padStart(2, '0'); // 23\n     const chunk = $('Webhook').item.json.body.chunk_number;\n    const id = $('Loop Over Items').item.json.id;\n\n    return yy + hh + mm + mi + dd + '-' + chunk + '.' + id;\n  })()\n}}"}, {"name": "documents", "value": "={{ $('Loop Over Items').item.json.content }}"}, {"name": "sources", "value": "={{$('Webhook').item.json.body.source}}"}]}, "options": {}}, "id": "284c0da4-57c4-41eb-9b24-76ab37942017", "name": "Set ChromaDB Payload", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [2224, 80]}, {"parameters": {"requestMethod": "POST", "url": "http://host.docker.internal:5555/add", "options": {}, "bodyParametersUi": {"parameter": [{"name": "=ids", "value": "={{ $('Set ChromaDB Payload').item.json.ids }}"}, {"name": "documents", "value": "={{ $('Set ChromaDB Payload').item.json.documents }}"}, {"name": "embedding", "value": "={{ $('Set ChromaDB Payload').item.json.embedding }}"}, {"name": "metedatas", "value": "={\n  \"Source\": \"{{ $('Set ChromaDB Payload').item.json.sources }}\",\n  \"Title\": \"{{ $('Loop Over Items').item.json.title }}\",\n  \"Remark\": \"{{ $('Loop Over Items').item.json.remark }}\"\n}"}]}, "headerParametersUi": {"parameter": [{"name": "Content-Type", "value": "application/json"}]}}, "id": "e5d9d711-bd9a-4335-ad55-886f214f8e09", "name": "Save to ChromaDB", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [2480, 80]}, {"parameters": {"promptType": "define", "text": "={{ $('Webhook').item.json.body.text }}", "hasOutputParser": true, "options": {"systemMessage": "You are a smart assistant to help understand information from extracted PDF file. Separate the content into chunks based on their meaning. Each chunk must cover a complete topic or concept; no important message should be split across chunks.\n\nAdditional Requirements:\n\n1. If the text is clearly under a title, include that title in the \"title\" field.\n2. Create a \"remark\" field with the following rules:\n   - Always start with \"<object> <model> — <short description>\" when a specific object/product/tool and model are mentioned in the content.\n     * OBJECT must be a generic category in lowercase (e.g., router, camera, DVR, switch, server, access point, door controller, intercom, barrier gate, UPS, NVR, device).\n     * MODEL must be written exactly as in the text (no changes, no inventions).\n     * If the model is missing, use \"model unknown\".\n   - If the content is generic or thematic (e.g., financial report, novel, safety manual), summarize the topic and document type instead (e.g., \"financial report — Q2 revenue details\").\n   - If no clear topic or object is available, create a general description of the content.\n   - Keep the description after the dash short and specific (6–12 words).\n   - Do not include linking words in the OBJECT or MODEL section (e.g., “the”, “a”, “an”, “to”).\n3. Maintain a consistent dash format: OBJECT MODEL — description.\n4. Use lowercase for the object, keep the model exactly as in source text, and use sentence case for the description.\n\nOutput Format:\n\nOutput ONLY a JSON array.\nDo not include any text, code block markers, or explanations before or after the JSON array.\n\nEach array item must be an object with:\n\"id\" (integer)\n\"content\" (string)\n\"title\" (string, optional if not applicable)\n\"remark\" (string)\n\nExample:\n[\n  {\n    \"id\": 1,\n    \"content\": \"...\",\n    \"title\": \"Environmental Specifications\",\n    \"remark\": \"router XR500 — operating temperature range\"\n  },\n  {\n    \"id\": 2,\n    \"content\": \"...\",\n    \"title\": \"Environmental Specifications\",\n    \"remark\": \"dvr DVR-1020 — operating temperature range\"\n  },\n  {\n    \"id\": 3,\n    \"content\": \"...\",\n    \"title\": \"Financial Summary\",\n    \"remark\": \"financial report — Q2 revenue details\"\n  },\n  {\n    \"id\": 4,\n    \"content\": \"...\",\n    \"title\": \"Getting Started\",\n    \"remark\": \"user guide — basic setup instructions\"\n  }\n]\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.1, "position": [1200, 176], "id": "9a8cac8b-ac37-4ce1-87f8-5177b0cc0f07", "name": "AI Agent", "retryOnFail": true, "maxTries": 5, "waitBetweenTries": 1500}, {"parameters": {"model": "qwen3:32b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [1200, 368], "id": "0de7893c-8dce-491a-9654-ea295079e637", "name": "<PERSON><PERSON><PERSON> Chat Model", "credentials": {"ollamaApi": {"id": "yEnEIhPEbVPuZH4a", "name": "Ollama account"}}}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"id\": { \"type\": \"integer\" },\n      \"content\": { \"type\": \"string\" },\n      \"title\": { \"type\": \"string\" },\n      \"remark\": { \"type\": \"string\" }\n    },\n    \"required\": [\"id\", \"content\", \"title\", \"remark\"]\n  }\n}\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [1392, 368], "id": "04b30b14-5307-45c8-ba43-b153dcc713fe", "name": "Structured Output Parser"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1776, 64], "id": "d27775ac-385d-4e82-80f3-ee4fbe9a4563", "name": "Loop Over Items"}, {"parameters": {"jsCode": "// Get the output array from the first item\nconst chunks = items[0].json.output;\n\nreturn chunks.map(chunk => {\n  return {\n    json: chunk\n  };\n});\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1584, 176], "id": "2420339c-ee80-4d70-9170-20731610b757", "name": "Separate chunks to items"}, {"parameters": {"method": "POST", "url": "http://host.docker.internal:5555/plan_b_piece_complete", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "{\"Content-Type\":\"application/json\"}", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"session_id\": \"{{ $('Webhook').item.json.body.session_id }}\",\n    \"chunk_id\": \"{{ $('Webhook').item.json.body.chunk_number }}\",\n    \"status\": \"success\"\n  }", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2160, 288], "id": "75ef082c-dd92-4283-a912-2ad20830a686", "name": "HTTP Request"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "41dcc66c-1c31-46f8-bdfe-1382f48d2731", "leftValue": "={{ $('Edit Fields').item.json.isLastItem }}", "rightValue": "true", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1952, 304], "id": "347ebdcd-d19e-4a3b-8499-f8959ad625a9", "name": "If"}, {"parameters": {"assignments": {"assignments": [{"id": "f98deab7-2c6b-42d1-87d3-2a27d911240a", "name": "isLastItem", "value": "={{ $itemIndex === $items().length - 1 }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1776, 304], "id": "984ae508-3806-4591-8407-7697a374ef51", "name": "<PERSON>"}, {"parameters": {"httpMethod": "POST", "path": "78e2736e-cfcf-4c1c-bc5d-b004da4e13b7", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [976, 176], "id": "d01431c0-0ae2-4bc5-ac14-d400131514ad", "name": "Webhook", "webhookId": "78e2736e-cfcf-4c1c-bc5d-b004da4e13b7"}], "pinData": {}, "connections": {"Get Embeddings1": {"main": [[{"node": "Set ChromaDB Payload", "type": "main", "index": 0}]]}, "Set ChromaDB Payload": {"main": [[{"node": "Save to ChromaDB", "type": "main", "index": 0}]]}, "Ollama Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Separate chunks to items", "type": "main", "index": 0}], []]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Get Embeddings1", "type": "main", "index": 0}]]}, "Save to ChromaDB": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Separate chunks to items": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Loop Over Items", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[]]}, "Edit Fields": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "timezone": "Asia/Singapore", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "f3a56b19-937d-4789-8a01-7ee0784a78b7", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e03cb1825228004b62e0a6d97f886e4ed3c55f131de25c4d61250f2f6671512a"}, "id": "cYtm7nlmuKxb1vxl", "tags": []}