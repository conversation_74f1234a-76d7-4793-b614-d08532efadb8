"""
Configuration settings for the ChromaDB server application.
"""

import os

# File upload configuration
ALLOWED_EXTENSIONS = {'pdf'}

# ChromaDB configuration
COLLECTION_NAME = "my_pdf_collection"

# N8N webhook URLs
N8N_WEBHOOK_URL = "http://localhost:5678/webhook/726e92eb-d600-484b-8f90-3c1f8ca38402"
N8N_ONFAIL_WEBHOOK_URL = "http://localhost:5678/webhook/78e2736e-cfcf-4c1c-bc5d-b004da4e13b7"

# Frontend (Question Page) status webhook for real-time upload notifications
# Use https with self-signed cert on port 5003 by default. If Chroma runs in Docker,
# consider changing 'localhost' to 'host.docker.internal'.
FRONTEND_STATUS_URL = "https://localhost:5003/api/upload-status"
# Whether to verify SSL cert for the above URL (False to ignore self-signed warnings)
FRONTEND_STATUS_VERIFY = False

# Public base URL for callbacks back into this server (for containers to reach host)
# If n8n runs in Docker, use host.docker.internal to reach the host's Chroma server
CALLBACK_PUBLIC_BASE = "http://host.docker.internal:5555"

# Chunk processing configuration
CHUNK_PROCESSING_TIMEOUT = 300
MAX_TOKENS_PER_CHUNK = 2000
CHARS_PER_TOKEN = 4

# GPU rest delay between chunks (in seconds)
GPU_REST_DELAY = 5  # Default 5 seconds delay between chunks

# Reranking functionality removed for simplicity

# Enhanced Query Performance Configuration (Legacy - kept for backward compatibility)
QUERY_ENHANCEMENT_CONFIG = {
    "adaptive_results": True,
    "keyword_matching_weight": 0.3,
    "semantic_weight": 0.7,
    "diversity_penalty_max": 0.3,
    "min_result_count": 8,
    "max_result_count": 25,
    "default_result_count": 15
}

# Intelligent Query Configuration
INTELLIGENT_QUERY_CONFIG = {
    "ollama_url": "http://localhost:11434",
    "ollama_model": "gpt-oss:20b",  # Default model (for backward compatibility)
    "keyword_extraction_model": "gpt-oss:20b",  # Model for keyword extraction
    "chunk_verification_model": "phi4-mini-reasoning:3.8b",  # Model for chunk verification
    "ollama_timeout": 45,
    "batch_size": 30,
    "max_results": 10,
    "confidence_threshold": 0.65,
    # Hybrid search configuration
    "keyword_filtered_chunks": 20,  # Number of chunks to get from keyword filtering
    "embedding_similarity_chunks": 30,  # Number of chunks to get from embedding similarity
    "no_keywords_fallback_chunks": 50,  # Number of chunks when no keywords are found
    # Reranking removed - using simple distance-based ranking
    # Advanced reranking configuration
    "enable_advanced_reranking": True,  # Enable sophisticated reranking algorithm
    "keyword_boost_factor": 1.5,  # Boost score for keyword-matched chunks
    "diversity_penalty": 0.1,  # Penalty for similar chunks (0.0 = no penalty, 1.0 = max penalty)
    "metadata_boost_factors": {  # Boost based on metadata fields
        "Title": 1.3,  # Chunks from titles get higher priority
        "Remark": 1.1   # Chunks from remarks get slight boost
    },
    "keyword_extraction_prompt": (
    "You are a strict keyword extractor.\n"
    "Your task is to extract 2 to 4 of the most important words or phrases from the user query **exactly as written**.\n\n"
    "Rules:\n"
    "- Extract ONLY important content words or short phrases (e.g., objects, subjects, key actions).\n"
    "- Phrases must appear exactly as-is in the query.\n"
    "- Do NOT include linking words such as 'how', 'what', 'why', 'the', 'is', 'can', 'to', 'and', 'for', etc.\n"
    "- Do NOT include question words or grammar words.\n"
    "- Use only original words from the query — no changes or explanations.\n"
    "- Output a single line with keywords or phrases, separated by commas.\n"
    "- Do NOT include any extra text, labels, quotes, or formatting.\n\n"
    "Query: \"{user_message}\"\n\n"
    "Output:"
    ),
    "chunk_verification_prompt": "Rate 0-1 how relevant this text is to answering: '{user_message}'\n\nText: '{chunk_content}'\n\nReturn ONLY a single number with two decimal places (e.g., 0.75). Do not include any other text or explanations."
}

# Reranking configuration removed

# Available models: "qwen3:32b", "phi4-mini-reasoning:3.8b", "gpt-oss:20b"
# Model separation strategy:
# - gpt-oss:20b: Better for keyword extraction (concise, focused output)
# - phi4-mini-reasoning:3.8b: Better for chunk verification (detailed reasoning)


# RapidAPI key for YouTube transcript extraction when processing deferred YouTube jobs
RAPIDAPI_KEY = os.getenv("RAPIDAPI_KEY", "")

# Reranking functionality removed - using distance-based ranking
