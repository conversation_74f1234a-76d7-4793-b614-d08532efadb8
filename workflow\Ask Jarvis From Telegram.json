{"name": "<PERSON> From Telegram", "nodes": [{"parameters": {"method": "POST", "url": "http://host.docker.internal:5555/query", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "{\n  \"Content-Type\": \"application/json\"\n}\n", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"embedding\": \"{{$('Get Embeddings1').item.json.embedding}}\",\n    \"source_filter\": \"{{$('Source Filter').item.json.filename}}\",\n    \"query_text\": \"{{ $('Source Filter').item.json.question }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1200, 288], "id": "d0411797-48d9-49a9-8200-f0fae1771122", "name": "Query ChromaDB"}, {"parameters": {"promptType": "define", "text": "={{ $('Webhook').item.json.body.message }}", "options": {"systemMessage": "=You are an helpful assistant, name JARVI<PERSON>.\nImportant:\n- Never answer with information that not exist in the knowledge base.\n- If the user message is not a question, just reply him in general.\n- Your answer should be easy to read and understand.\n\nThis is your knowledge base, you only can answer user question based on it:\n{{ $('Query ChromaDB').item.json.results.documents }}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.1, "position": [1392, 288], "id": "48685653-e920-4cd0-96f6-c6fbf528e39f", "name": "AI Agent"}, {"parameters": {"requestMethod": "POST", "url": "http://host.docker.internal:11434/api/embeddings", "jsonParameters": true, "options": {"bodyContentType": "json"}, "bodyParametersJson": "={\n  \"model\": \"all-minilm:33m\",\n  \"prompt\": \"{{ $('Source Filter').item.json.question }}\"\n}\n ", "headerParametersJson": "{\"Content-Type\":\"application/json\"}"}, "id": "924c84ba-5d3b-40d9-8618-fa2dcce92023", "name": "Get Embeddings1", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1008, 288]}, {"parameters": {"model": "gpt-oss:20b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [1392, 480], "id": "8a76c8be-0521-40cb-aee2-a98d0e1f7736", "name": "<PERSON><PERSON><PERSON> Chat Model", "credentials": {"ollamaApi": {"id": "yEnEIhPEbVPuZH4a", "name": "Ollama account"}}}, {"parameters": {"jsCode": "// Get the input from the previous node\nconst input = items[0].json.output;\n\n// Remove <think>...</think> including line breaks and inner content\nconst cleaned = input.replace(/<think>[\\s\\S]*?<\\/think>/, '').trim();\n\n// Return the cleaned output in a new field\nreturn [{ json: { text: cleaned } }];;\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1760, 288], "id": "7544664c-07c7-4e95-91f3-98687b6ebff3", "name": "Format Ollama Output"}, {"parameters": {"jsCode": "const input = $json.body.message;\n\n// Regex: Match @<filename with spaces>.ext (greedy until dot-ext, stops at next space or end)\nconst match = input.match(/@(.+?\\.[a-zA-Z0-9]+)(?=\\s|$)/);\n\nlet filename = null;\nlet question = input;\n\nif (match) {\n  filename = match[1].trim();\n  // Remove the matched filename (with @) from the input\n  question = input.replace(match[0], '').trim().replace(/\\s+/g, ' ').trim();\n}\n\nreturn {\n  question,\n  filename,\n};\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [832, 288], "id": "3aa591a7-4df9-41a4-92b7-22be5d6d06eb", "name": "Source Filter"}, {"parameters": {"method": "POST", "url": "http://host.docker.internal:5003/api/telegram-reply", "sendBody": true, "bodyParameters": {"parameters": [{"name": "output", "value": "={{ $json.output || $json.text }}"}, {"name": "chat_id", "value": "={{ $('Webhook').item.json.body.original_message.chat_id }}"}, {"name": "message_id", "value": "={{ $('Webhook').item.json.body.original_message.message_id }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1952, 288], "id": "6bc35c39-a1fe-44e6-8971-116b5414d271", "name": "HTTP Request"}, {"parameters": {"httpMethod": "POST", "path": "dc48add0-1df2-4670-837f-71ce101b473a", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [608, 288], "id": "94d4ba20-25c8-418d-86db-7d086def773a", "name": "Webhook", "webhookId": "dc48add0-1df2-4670-837f-71ce101b473a"}], "pinData": {}, "connections": {"Query ChromaDB": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Format Ollama Output", "type": "main", "index": 0}]]}, "Get Embeddings1": {"main": [[{"node": "Query ChromaDB", "type": "main", "index": 0}]]}, "Ollama Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Format Ollama Output": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Source Filter": {"main": [[{"node": "Get Embeddings1", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Source Filter", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "6fc53449-ee12-4e3a-b7b6-44f45aebe9ba", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e03cb1825228004b62e0a6d97f886e4ed3c55f131de25c4d61250f2f6671512a"}, "id": "g8ZkjhrfevaK7B5Z", "tags": []}