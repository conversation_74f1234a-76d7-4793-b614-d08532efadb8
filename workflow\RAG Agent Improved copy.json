{"name": "RAG Agent Improved copy", "nodes": [{"parameters": {"httpMethod": "POST", "path": "726e92eb-d600-484b-8f90-3c1f8ca38402", "options": {"binaryData": true}}, "id": "053777ee-2cb4-433b-8b17-4a51af3cc857", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [816, 176], "webhookId": "726e92eb-d600-484b-8f90-3c1f8ca38402"}, {"parameters": {"requestMethod": "POST", "url": "http://host.docker.internal:11434/api/embeddings", "jsonParameters": true, "options": {"bodyContentType": "json"}, "bodyParametersJson": "{\n  \"model\": \"snowflake-artic-embed2:568m\",\n  \"prompt\": \"$('AI Agent').item.json.output\"\n}\n", "headerParametersJson": "{\"Content-Type\":\"application/json\"}"}, "id": "b03c88ed-91a2-4f1c-8d93-10cfb5f0ca37", "name": "Get Embeddings1", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1984, 64]}, {"parameters": {"values": {"string": [{"name": "ids", "value": "={{\n  (() => {\n    const now = new Date(new Date().toLocaleString(\"en-US\", { timeZone: \"Asia/Kuala_Lumpur\" }));\n    const yy = now.getFullYear().toString().slice(-2);  // 25\n    const hh = now.getHours().toString().padStart(2, '0'); // 10\n    const mm = (now.getMonth()+1).toString().padStart(2, '0'); // 07 (Jan=0)\n    const mi = now.getMinutes().toString().padStart(2, '0'); // 40\n    const dd = now.getDate().toString().padStart(2, '0'); // 23\n    const chunk = $('Extract From File').item.json.data.chunk_number;\n    const id = $('Loop Over Items').item.json.id;\n\n    return yy + hh + mm + mi + dd + '-' + chunk + '.' + id;\n  })()\n}}"}, {"name": "documents", "value": "={{ $('Loop Over Items').item.json.content }}"}, {"name": "sources", "value": "={{$('Extract From File').item.json.data.source}}"}]}, "options": {}}, "id": "74113a97-9591-41fb-af25-f192841cbc0b", "name": "Set ChromaDB Payload", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [2192, 64]}, {"parameters": {"requestMethod": "POST", "url": "http://host.docker.internal:5555/add", "options": {}, "bodyParametersUi": {"parameter": [{"name": "=ids", "value": "={{ $('Set ChromaDB Payload').item.json.ids }}"}, {"name": "documents", "value": "={{ $('Set ChromaDB Payload').item.json.documents }}"}, {"name": "embedding", "value": "={{ $('Set ChromaDB Payload').item.json.embedding }}"}, {"name": "metadatas", "value": "={\n  \"Source\": \"{{ $('Set ChromaDB Payload').item.json.sources }}\",\n  \"Title\": \"{{ $('Loop Over Items').item.json.title }}\",\n  \"Remark\": \"{{ $('Loop Over Items').item.json.remark }}\"\n}"}]}, "headerParametersUi": {"parameter": [{"name": "Content-Type", "value": "application/json"}]}}, "id": "ca9d8ab7-3ddf-4a8d-8537-19e6dd51642f", "name": "Save to ChromaDB", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [2400, 64]}, {"parameters": {"promptType": "define", "text": "={{ $('Extract From File').item.json.data.text }}", "hasOutputParser": true, "options": {"systemMessage": "You are a smart assistant to help understand information from extracted PDF file. Separate the content into chunks based on their meaning. Each chunk must cover a complete topic or concept; no important message should be split across chunks.\n\nAdditional Requirements:\n\n1. If the text is clearly under a title, include that title in the \"title\" field.\n2. Create a \"remark\" field with the following rules:\n   - Always start with \"<object> <model> — <short description>\" when a specific object/product/tool and model are mentioned in the content.\n     * OBJECT must be a generic category in lowercase (e.g., router, camera, DVR, switch, server, access point, door controller, intercom, barrier gate, UPS, NVR, device).\n     * MODEL must be written exactly as in the text (no changes, no inventions).\n     * If the model is missing, use \"model unknown\".\n   - If the content is generic or thematic (e.g., financial report, novel, safety manual), summarize the topic and document type instead (e.g., \"financial report — Q2 revenue details\").\n   - If no clear topic or object is available, create a general description of the content.\n   - Keep the description after the dash short and specific (6–12 words).\n   - Do not include linking words in the OBJECT or MODEL section (e.g., “the”, “a”, “an”, “to”).\n3. Maintain a consistent dash format: OBJECT MODEL — description.\n4. Use lowercase for the object, keep the model exactly as in source text, and use sentence case for the description.\n\nOutput Format:\n\nOutput ONLY a JSON array.\nDo not include any text, code block markers, or explanations before or after the JSON array.\n\nEach array item must be an object with:\n\"id\" (integer)\n\"content\" (string)\n\"title\" (string, optional if not applicable)\n\"remark\" (string)\n\nExample:\n[\n  {\n    \"id\": 1,\n    \"content\": \"...\",\n    \"title\": \"Environmental Specifications\",\n    \"remark\": \"router XR500 — operating temperature range\"\n  },\n  {\n    \"id\": 2,\n    \"content\": \"...\",\n    \"title\": \"Environmental Specifications\",\n    \"remark\": \"dvr DVR-1020 — operating temperature range\"\n  },\n  {\n    \"id\": 3,\n    \"content\": \"...\",\n    \"title\": \"Financial Summary\",\n    \"remark\": \"financial report — Q2 revenue details\"\n  },\n  {\n    \"id\": 4,\n    \"content\": \"...\",\n    \"title\": \"Getting Started\",\n    \"remark\": \"user guide — basic setup instructions\"\n  }\n]\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.1, "position": [1200, 176], "id": "b38653f7-b8ee-4086-ab06-9b19634b86ff", "name": "AI Agent", "retryOnFail": true, "maxTries": 2, "onError": "continueErrorOutput"}, {"parameters": {"model": "qwen3:32b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [1200, 368], "id": "9a3d75b7-b83c-4dbf-a86f-11ce8c326595", "name": "<PERSON><PERSON><PERSON> Chat Model", "credentials": {"ollamaApi": {"id": "yEnEIhPEbVPuZH4a", "name": "Ollama account"}}}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"id\": { \"type\": \"integer\" },\n      \"content\": { \"type\": \"string\" },\n      \"title\": { \"type\": \"string\" },\n      \"remark\": { \"type\": \"string\" }\n    },\n    \"required\": [\"id\", \"content\", \"title\", \"remark\"]\n  }\n}\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [1392, 368], "id": "b67db46d-cc8a-4f08-8125-e8aeb29a93be", "name": "Structured Output Parser"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1776, 48], "id": "8f124212-6391-4f3f-96b6-8d0575b5b209", "name": "Loop Over Items"}, {"parameters": {"jsCode": "// Get the output array from the first item\nconst chunks = items[0].json.output;\n\nreturn chunks.map(chunk => {\n  return {\n    json: chunk\n  };\n});\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1584, 160], "id": "332c65f8-665f-46ba-80c9-f91c67c2cfab", "name": "Separate chunks to items"}, {"parameters": {"method": "POST", "url": "http://host.docker.internal:5555/chunk_complete", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "{\"Content-Type\":\"application/json\"}", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"session_id\": \"{{ $('Extract From File').item.json.data.session_id }}\",\n    \"chunk_id\": \"{{ $('Extract From File').item.json.data.chunk_number }}\",\n    \"status\": \"success\"\n  }", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2192, 240], "id": "98ce3061-5a27-49af-8780-cdd9446d464e", "name": "HTTP Request"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "41dcc66c-1c31-46f8-bdfe-1382f48d2731", "leftValue": "={{ $('Edit Fields').item.json.isLastItem }}", "rightValue": "true", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1984, 256], "id": "e360f703-224b-4e0a-a8f9-1faf9f73d983", "name": "If"}, {"parameters": {"assignments": {"assignments": [{"id": "f98deab7-2c6b-42d1-87d3-2a27d911240a", "name": "isLastItem", "value": "={{ $itemIndex === $items().length - 1 }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1776, 256], "id": "b52d6fdc-a84f-43cc-ba6f-3526edbb9a68", "name": "<PERSON>"}, {"parameters": {"operation": "fromJson", "options": {}}, "id": "5e394813-b756-4a42-a909-5d7fe8d24ad1", "name": "Extract From File", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1008, 176]}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o-2024-05-13", "mode": "list", "cachedResultName": "gpt-4o-2024-05-13"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [992, 528], "id": "e29f26f4-5e57-4089-bbc9-6338e5c2fc20", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "6nvnQqjbfeTZXSZ8", "name": "OpenAi account"}}}, {"parameters": {"method": "POST", "url": "http://host.docker.internal:5555/plan_b", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "{\"Content-Type\":\"application/json\"}", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json.data }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1584, 480], "id": "bb27624c-cd80-43ed-a44c-041b0a5aab1c", "name": "HTTP Request1"}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Extract From File", "type": "main", "index": 0}]]}, "Get Embeddings1": {"main": [[{"node": "Set ChromaDB Payload", "type": "main", "index": 0}]]}, "Set ChromaDB Payload": {"main": [[{"node": "Save to ChromaDB", "type": "main", "index": 0}]]}, "Ollama Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Separate chunks to items", "type": "main", "index": 0}], [{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Get Embeddings1", "type": "main", "index": 0}]]}, "Save to ChromaDB": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Separate chunks to items": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Loop Over Items", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[]]}, "Edit Fields": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Extract From File": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[]]}}, "active": true, "settings": {"executionOrder": "v1", "timezone": "Asia/Singapore", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "f4645fc5-fbe4-49a3-a4ae-72a45fa3d1fa", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e03cb1825228004b62e0a6d97f886e4ed3c55f131de25c4d61250f2f6671512a"}, "id": "gZGzapzND45Ry6Fl", "tags": []}