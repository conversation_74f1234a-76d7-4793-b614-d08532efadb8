# Local Configuration
# Your actual configuration values

import os

# ChromaDB Configuration (should match your existing setup)
CHROMADB_BASE_URL = "http://localhost:5555"

# n8n Webhook Configuration for different AI models
# ChatGPT Model Webhook URLs
#CHATGPT_WEBHOOK_URL = 'http://localhost:5678/webhook-test/3e04cd5f-9510-4364-b44f-3d2e3f6a572f' # test url
CHATGPT_WEBHOOK_URL = 'http://localhost:5678/webhook/3e04cd5f-9510-4364-b44f-3d2e3f6a572f' # prod url

# RSJARVIS Model Webhook URLs
#RSJARVIS_WEBHOOK_URL = 'http://localhost:5678/webhook-test/a9d20474-dea8-4c13-8b3b-14aa71847780' # test url
RSJARVIS_WEBHOOK_URL = 'http://localhost:5678/webhook/a9d20474-dea8-4c13-8b3b-14aa71847780' # prod url

# TELEGRAM Model Webhook URLs
#TELEGRAM_WEBHOOK_URL = 'http://localhost:5678/webhook-test/dc48add0-1df2-4670-837f-71ce101b473a' # test url
TELEGRAM_WEBHOOK_URL = 'http://localhost:5678/webhook/dc48add0-1df2-4670-837f-71ce101b473a' # prod url

# Model Configuration
AI_MODELS = {
    'RSJARVIS': {
        'name': 'RSJARVIS',
        'webhook_url': os.getenv('RSJARVIS_WEBHOOK_URL', RSJARVIS_WEBHOOK_URL)
    }
}

# Default model
DEFAULT_MODEL = 'RSJARVIS'

# Legacy support still points to DEFAULT_MODEL webhook
N8N_WEBHOOK_URL = AI_MODELS[DEFAULT_MODEL]['webhook_url']

# Groq Configuration
# Groq api key: ********************************************************
# Model: whisper-large-v3
GROQ_API_KEY = os.getenv('GROQ_API_KEY', "********************************************************")
GROQ_MODEL = "whisper-large-v3"

# Flask Configuration
SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-for-production-change-this')
DEBUG = True
HOST = '0.0.0.0'
PORT = 5003  # Changed from 5001 to 5003

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', '**********************************************')
# Use the same webhook URL pattern as other models
TELEGRAM_WEBHOOK_URL = os.getenv('TELEGRAM_WEBHOOK_URL', 'http://localhost:5678/webhook/dc48add0-1df2-4670-837f-71ce101b473a')
TELEGRAM_POLLING_INTERVAL = int(os.getenv('TELEGRAM_POLLING_INTERVAL', '5'))  # seconds
TELEGRAM_PROCESSED_MESSAGES_FILE = os.getenv('TELEGRAM_PROCESSED_MESSAGES_FILE', 'telegram_processed_messages.json')

# Logging Configuration
LOG_LEVEL = 'INFO'


# ----- Local HTTPS and Login Settings -----
REQUIRE_LOGIN = True
# Primary fallback user (kept for compatibility)
LOGIN_USERNAME = 'superadmin'
LOGIN_PASSWORD = '5uP3r@dMin:D'
# Multiple login users supported
LOGIN_USERS = {
    'superadmin': '5uP3r@dMin:D',
    'admin': 'Admin123'
}

# SSL Configuration - HTTPS Required
SSL_ENABLED = True  # HTTPS is required for security
SSL_CERT_PATH = 'certs/cert.pem'
SSL_KEY_PATH = 'certs/key.pem'

# Note: HTTPS with auto-regenerating certificates
# 1. Certificates auto-regenerate if expired
# 2. Run install_certificate.ps1 as Administrator to trust the certificate
# 3. Access via https://************:5003
