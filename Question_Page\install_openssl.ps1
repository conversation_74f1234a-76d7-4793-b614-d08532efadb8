# Attempts to install OpenSSL via winget or Chocolatey
$ErrorActionPreference = 'Stop'

function Test-Command($name) {
  try { Get-Command $name -ErrorAction Stop | Out-Null; return $true } catch { return $false }
}

if (Test-Command openssl) {
  Write-Host "OpenSSL already present"
  exit 0
}

# Try winget first
if (Test-Command winget) {
  Write-Host "Installing OpenSSL via winget..."
  # Common package: Shining Light Productions
  winget install -e --id ShiningLight.OpenSSL.Light -h --accept-package-agreements --accept-source-agreements
  if (Test-Command openssl) { Write-Host "OpenSSL installed (winget)"; exit 0 }
}

# Try Chocolatey
if (Test-Command choco) {
  Write-Host "Installing OpenSSL via Chocolatey..."
  choco install openssl.light -y --no-progress
  if (Test-Command openssl) { Write-Host "OpenSSL installed (choco)"; exit 0 }
}

Write-Host "Could not install OpenSSL automatically. Please install manually:"
Write-Host "- winget install ShiningLight.OpenSSL.Light"
Write-Host "- or choco install openssl.light"
exit 1

